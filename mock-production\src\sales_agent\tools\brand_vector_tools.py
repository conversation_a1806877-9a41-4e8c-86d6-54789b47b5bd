"""
品牌向量工具工厂 - 根据品牌ID提供对应的向量查询工具
"""
import logging
from typing import List, Dict, Any, Optional
from llama_index.core.tools import FunctionTool

from ..services.vector_store_service import VectorStoreService
from .base_tools import BaseTools

logger = logging.getLogger(__name__)


class BrandVectorToolsFactory:
    """品牌向量工具工厂类"""

    def __init__(self):
        self.vector_store = VectorStoreService()
        self.is_initialized = False
        self.brand_tools = {}  # 缓存不同品牌的工具实例
        logger.info("🏭 初始化品牌向量工具工厂")

    def initialize(self):
        """初始化向量存储"""
        try:
            if not self.is_initialized:
                # 只有在需要时才初始化
                logger.info("🔧 按需初始化品牌向量工具工厂...")
                self.vector_store.initialize()
                self.is_initialized = True
                logger.info("✅ 品牌向量工具工厂初始化完成")
        except Exception as e:
            logger.error(f"❌ 品牌向量工具工厂初始化失败: {e}")
            # 不抛出异常，允许工具继续工作
            logger.warning("⚠️ 品牌向量工具工厂将在离线模式下工作")

    def get_brand_tools(self, brand_id: str) -> 'BrandVectorTools':
        """
        根据品牌ID获取对应的向量查询工具

        Args:
            brand_id: 品牌ID (如: 'qinggong', 'ykwy', 'nike' 等)

        Returns:
            对应品牌的向量查询工具实例
        """
        if brand_id not in self.brand_tools:
            if not self.is_initialized:
                self.initialize()

            # 创建品牌专用工具实例
            self.brand_tools[brand_id] = BrandVectorTools(brand_id, self.vector_store)
            logger.info(f"🔧 为品牌 {brand_id} 创建向量查询工具")

        return self.brand_tools[brand_id]

    def sync_brand_data(self, brand_id: str) -> Dict:
        """
        同步指定品牌的向量数据

        Args:
            brand_id: 品牌ID

        Returns:
            同步结果
        """
        try:
            if not self.is_initialized:
                self.initialize()

            logger.info(f"🔄 开始同步品牌 {brand_id} 的向量数据...")

            # 根据品牌ID调用不同的同步方法
            if brand_id == 'qinggong':
                return self._sync_qinggong_data()
            elif brand_id == 'ykwy':
                return self._sync_ykwy_data()
            else:
                return {
                    'success': False,
                    'message': f'暂不支持品牌 {brand_id} 的数据同步',
                    'data': {}
                }

        except Exception as e:
            logger.error(f"❌ 同步品牌 {brand_id} 数据失败: {e}")
            return {
                'success': False,
                'message': f'同步品牌 {brand_id} 数据失败: {str(e)}',
                'data': {}
            }

    def _sync_qinggong_data(self) -> Dict:
        """同步轻功体育数据"""
        # 目前只有轻功体育的数据，直接调用全量同步
        return self.vector_store.sync_all_data()

    def _sync_ykwy_data(self) -> Dict:
        """同步易客无忧数据"""
        # 未来实现易客无忧数据同步
        return {
            'success': False,
            'message': '易客无忧数据同步功能待实现',
            'data': {}
        }


class BrandVectorTools(BaseTools):
    """品牌专用向量查询工具"""

    def __init__(self, brand_id: str, vector_store: VectorStoreService):
        super().__init__()
        self.brand_id = brand_id
        self.vector_store = vector_store
        self.brand_name = self._get_brand_name(brand_id)
        logger.info(f"🔧 初始化 {self.brand_name} 向量查询工具")

    def _get_brand_name(self, brand_id: str) -> str:
        """根据品牌ID获取品牌名称"""
        brand_names = {
            'qinggong': '轻功体育',
            'ykwy': '易客无忧',
            'nike': '耐克',
            'adidas': '阿迪达斯'
        }
        return brand_names.get(brand_id, brand_id)

    def _get_collection_suffix(self) -> str:
        """获取品牌专用的集合后缀"""
        return f"_{self.brand_id}"

    def search_products_by_vector(self, query: str, limit: int = 5, status_filter: str = "已上架") -> str:
        """
        搜索品牌专用产品信息

        Args:
            query: 搜索查询文本
            limit: 返回结果数量限制，默认5，最大20
            status_filter: 产品状态过滤，默认'已上架'，可设置为None搜索所有状态

        Returns:
            格式化的产品搜索结果文本
        """
        # 记录输入参数
        logger.info(f"🔧 [{self.brand_name}] search_products_by_vector 调用")
        logger.info(f"📥 输入参数: query='{query}', limit={limit}, status_filter='{status_filter}'")

        try:
            # 限制最大返回数量
            limit = min(limit, 20)

            logger.info(f"🔍 [{self.brand_name}] 向量搜索产品: {query} (limit={limit}, status={status_filter})")

            # 使用品牌过滤条件
            filters = {'brand_id': self.brand_id}
            if status_filter:
                filters['status'] = status_filter

            results = self.vector_store.search_products(query, limit, filters)

            if not results:
                return f"未找到相关的{self.brand_name}产品信息。"

            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results, 1):
                score_percent = f"{result.score * 100:.1f}%"
                formatted_results.append(f"""{self.brand_name}产品 {i} (相似度: {score_percent}):
{result.content}
---""")

            response = f"找到 {len(results)} 个相关的{self.brand_name}产品:\n\n" + "\n".join(formatted_results)
            logger.info(f"✅ [{self.brand_name}] 向量搜索产品完成，返回 {len(results)} 个结果")

            # 记录返回值
            logger.info(f"📤 返回值长度: {len(response)} 字符")
            logger.info(f"📤 返回值预览: {response[:200]}...")

            return response

        except Exception as e:
            error_msg = f"搜索{self.brand_name}产品时出现错误: {str(e)}"
            logger.error(f"❌ [{self.brand_name}] 向量搜索产品失败: {e}")
            logger.info(f"📤 错误返回值: {error_msg}")
            return error_msg

    def get_popular_products(self, category: str = None, limit: int = 8) -> str:
        """
        获取热门/推荐产品列表

        Args:
            category: 产品类别过滤（可选）
            limit: 返回结果数量，默认8，最大15

        Returns:
            格式化的热门产品列表
        """
        # 记录输入参数
        logger.info(f"🔧 [{self.brand_name}] get_popular_products 调用")
        logger.info(f"📥 输入参数: category='{category}', limit={limit}")

        try:
            limit = min(limit, 15)
            logger.info(f"🔥 [{self.brand_name}] 获取热门产品 (category={category}, limit={limit})")

            # 使用通用查询词获取多样化的产品
            popular_queries = [
                "热门 推荐 畅销",
                "新品 上新 最新",
                "运动 健身 户外",
                "时尚 潮流 经典"
            ]

            all_results = []
            seen_ids = set()

            for query in popular_queries:
                filters = {'brand_id': self.brand_id, 'status': '已上架'}
                if category:
                    # 将类别添加到查询中
                    search_query = f"{category} {query}"
                else:
                    search_query = query

                results = self.vector_store.search_products(search_query, limit//2, filters)

                for result in results:
                    # 避免重复产品
                    product_id = result.metadata.get('product_id', '')
                    if product_id and product_id not in seen_ids:
                        seen_ids.add(product_id)
                        all_results.append(result)

                if len(all_results) >= limit:
                    break

            # 取前limit个结果
            final_results = all_results[:limit]

            if not final_results:
                category_text = f"{category}类别的" if category else ""
                return f"暂时没有找到{self.brand_name}{category_text}热门产品。"

            # 格式化结果
            formatted_results = []
            for i, result in enumerate(final_results, 1):
                # 简化产品信息显示
                content_lines = result.content.split('\n')
                product_name = content_lines[0] if content_lines else "未知产品"
                formatted_results.append(f"{i}. {product_name}")

            category_text = f"{category}类别" if category else "热门"
            response = f"🔥 {self.brand_name}{category_text}产品推荐 (共{len(final_results)}个):\n\n" + "\n".join(formatted_results)

            if len(final_results) >= 8:
                response += f"\n\n💡 如需查看具体产品详情，请告诉我您感兴趣的产品名称或类型。"

            logger.info(f"✅ [{self.brand_name}] 获取热门产品完成，返回 {len(final_results)} 个结果")

            # 记录返回值
            logger.info(f"📤 返回值长度: {len(response)} 字符")
            logger.info(f"📤 返回值预览: {response[:200]}...")

            return response

        except Exception as e:
            error_msg = f"获取{self.brand_name}热门产品时出现错误: {str(e)}"
            logger.error(f"❌ [{self.brand_name}] 获取热门产品失败: {e}")
            logger.info(f"📤 错误返回值: {error_msg}")
            return error_msg

    def search_qa_knowledge_by_vector(self, query: str, limit: int = 5) -> str:
        """
        搜索品牌专用问答知识库

        Args:
            query: 搜索查询文本
            limit: 返回结果数量限制，默认5

        Returns:
            格式化的问答搜索结果文本
        """
        # 记录输入参数
        logger.info(f"🔧 [{self.brand_name}] search_qa_knowledge_by_vector 调用")
        logger.info(f"📥 输入参数: query='{query}', limit={limit}")

        try:
            logger.info(f"🔍 [{self.brand_name}] 向量搜索问答知识库: {query}")

            # 使用品牌过滤条件
            filters = {'brand_id': self.brand_id}
            results = self.vector_store.search_qa_knowledge(query, limit, filters)

            if not results:
                return f"未找到相关的{self.brand_name}问答信息。"

            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results, 1):
                score_percent = f"{result.score * 100:.1f}%"
                formatted_results.append(f"""{self.brand_name}问答 {i} (相似度: {score_percent}):
{result.content}
---""")

            response = f"找到 {len(results)} 个相关的{self.brand_name}问答:\n\n" + "\n".join(formatted_results)
            logger.info(f"✅ [{self.brand_name}] 向量搜索问答知识库完成，返回 {len(results)} 个结果")

            # 记录返回值
            logger.info(f"📤 返回值长度: {len(response)} 字符")
            logger.info(f"📤 返回值预览: {response[:200]}...")

            return response

        except Exception as e:
            error_msg = f"搜索{self.brand_name}问答知识库时出现错误: {str(e)}"
            logger.error(f"❌ [{self.brand_name}] 向量搜索问答知识库失败: {e}")
            logger.info(f"📤 错误返回值: {error_msg}")
            return error_msg



    def intelligent_vector_qa(self, question: str) -> str:
        """
        品牌专用智能向量问答 - 综合搜索所有数据源

        Args:
            question: 用户问题

        Returns:
            综合搜索结果和智能建议
        """
        # 记录输入参数
        logger.info(f"🔧 [{self.brand_name}] intelligent_vector_qa 调用")
        logger.info(f"📥 输入参数: question='{question}'")

        try:
            logger.info(f"🤖 [{self.brand_name}] 智能向量问答: {question}")

            # 执行综合搜索，使用品牌过滤
            result = self.vector_store.intelligent_qa(question, None)  # 通过brand_id过滤

            if not result['success']:
                return f"{self.brand_name}智能问答失败: {result['message']}"

            data = result['data']
            products = [item for item in data['products'] if item.get('metadata', {}).get('brand_id') == self.brand_id]
            qa_knowledge = [item for item in data['qa_knowledge'] if item.get('metadata', {}).get('brand_id') == self.brand_id]
            size_charts = [item for item in data['size_charts'] if item.get('metadata', {}).get('brand_id') == self.brand_id]

            # 构建响应
            response_parts = []

            # 添加问答知识库结果（优先级最高）
            if qa_knowledge:
                response_parts.append(f"📚 {self.brand_name}相关问答:")
                for i, item in enumerate(qa_knowledge[:2], 1):
                    score_percent = f"{item['score'] * 100:.1f}%"
                    response_parts.append(f"{i}. (相似度: {score_percent})\n{item['content']}")
                response_parts.append("")

            # 添加产品信息
            if products:
                response_parts.append(f"🛍️ {self.brand_name}相关产品:")
                for i, item in enumerate(products[:2], 1):
                    score_percent = f"{item['score'] * 100:.1f}%"
                    response_parts.append(f"{i}. (相似度: {score_percent})\n{item['content']}")
                response_parts.append("")

            # 添加尺码表信息
            if size_charts:
                response_parts.append(f"📏 {self.brand_name}尺码信息:")
                for i, item in enumerate(size_charts[:1], 1):
                    score_percent = f"{item['score'] * 100:.1f}%"
                    response_parts.append(f"{i}. (相似度: {score_percent})\n{item['content']}")
                response_parts.append("")

            # 添加智能建议
            if result['suggestion']:
                response_parts.append(f"💡 {self.brand_name}专业建议: {result['suggestion']}")

            if not response_parts:
                return f"未找到相关的{self.brand_name}信息，建议联系{self.brand_name}客服。"

            response = "\n".join(response_parts)
            logger.info(f"✅ [{self.brand_name}] 智能向量问答完成")

            # 记录返回值
            logger.info(f"📤 返回值长度: {len(response)} 字符")
            logger.info(f"📤 返回值预览: {response[:200]}...")

            return response

        except Exception as e:
            error_msg = f"{self.brand_name}智能问答时出现错误: {str(e)}"
            logger.error(f"❌ [{self.brand_name}] 智能向量问答失败: {e}")
            logger.info(f"📤 错误返回值: {error_msg}")
            return error_msg

    def get_all_tools(self) -> List[FunctionTool]:
        """获取品牌专用的所有向量查询工具"""
        tools = [
            FunctionTool.from_defaults(
                fn=self.search_products_by_vector,
                name=f"search_{self.brand_id}_products_by_vector",
                description=f"【产品搜索】当客户询问具体产品如'跑步鞋'、'篮球装备'、'运动服'等时必须使用此工具。使用向量搜索{self.brand_name}已上架产品信息，支持语义搜索。参数：query（搜索查询文本），limit（可选，返回结果数量，默认5，最大20），status_filter（可选，产品状态过滤，默认'已上架'）"
            ),
            FunctionTool.from_defaults(
                fn=self.get_popular_products,
                name=f"get_{self.brand_id}_popular_products",
                description=f"【热门产品推荐】当客户说'推荐产品'、'热门产品'、'有什么产品'、'看看产品'时必须使用此工具。获取{self.brand_name}已上架的热门/推荐产品列表。参数：category（可选，产品类别），limit（可选，返回数量，默认8，最大15）"
            ),
            FunctionTool.from_defaults(
                fn=self.search_qa_knowledge_by_vector,
                name=f"search_{self.brand_id}_qa_by_vector",
                description=f"【问答知识库搜索】当客户询问'如何退货'、'怎么保养'、'有什么优惠'、'售后问题'等服务相关问题时必须使用此工具。搜索{self.brand_name}问答知识库。参数：query（搜索查询文本），limit（可选，返回结果数量，默认5）"
            ),

            FunctionTool.from_defaults(
                fn=self.intelligent_vector_qa,
                name=f"{self.brand_id}_intelligent_vector_qa",
                description=f"【综合智能问答】当客户问题复杂，涉及多个方面（如'我想买篮球鞋，有什么推荐，什么尺码合适'）时使用此工具。综合搜索所有{self.brand_name}数据源（产品、问答、尺码表）并提供智能建议。参数：question（用户问题）"
            )
        ]

        logger.info(f"🛠️ 为品牌 {self.brand_name} 注册了 {len(tools)} 个向量查询工具")
        return tools


# 全局品牌向量工具工厂实例
brand_vector_factory = BrandVectorToolsFactory()
