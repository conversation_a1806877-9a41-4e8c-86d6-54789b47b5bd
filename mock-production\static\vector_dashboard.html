<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>向量数据可视化仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .search-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 200px 1fr 100px auto;
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group select,
        .form-group input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .search-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .result-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
        }
        
        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .similarity-score {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .result-content {
            color: #333;
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .result-meta {
            font-size: 0.9em;
            color: #666;
            font-style: italic;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }
        
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 向量数据可视化仪表板</h1>
            <p>实时查看和搜索向量数据库中的内容</p>
        </div>
        
        <div class="content">
            <!-- 统计信息 -->
            <div id="statsSection">
                <h2 style="margin-bottom: 20px; color: #333;">📊 数据统计</h2>
                <div class="stats-grid" id="statsGrid">
                    <div class="loading">正在加载统计信息...</div>
                </div>
            </div>
            
            <!-- 搜索功能 -->
            <div class="search-section">
                <h2 style="margin-bottom: 20px; color: #333;">🔎 向量搜索</h2>
                <form id="searchForm" class="search-form">
                    <div class="form-group">
                        <label for="collection">数据集合</label>
                        <select id="collection" required>
                            <option value="">选择集合</option>
                            <option value="products">产品数据</option>
                            <option value="qa_knowledge">问答知识库</option>
                            <option value="size_charts">尺码表</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="query">搜索查询</label>
                        <input type="text" id="query" placeholder="输入搜索内容..." required>
                    </div>
                    <div class="form-group">
                        <label for="limit">结果数量</label>
                        <input type="number" id="limit" value="5" min="1" max="20">
                    </div>
                    <button type="submit" class="search-btn">搜索</button>
                </form>
                
                <div id="searchResults" class="results-section"></div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取统计信息
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
        });

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/vector/statistics');
                const data = await response.json();
                
                if (data.success) {
                    displayStatistics(data.data);
                } else {
                    document.getElementById('statsGrid').innerHTML = 
                        `<div class="error">加载统计信息失败: ${data.message}</div>`;
                }
            } catch (error) {
                document.getElementById('statsGrid').innerHTML = 
                    `<div class="error">加载统计信息出错: ${error.message}</div>`;
            }
        }

        // 显示统计信息
        function displayStatistics(stats) {
            const statsGrid = document.getElementById('statsGrid');
            let html = '';
            
            const collectionNames = {
                'products': '📦 产品数据',
                'qa_knowledge': '💬 问答知识库', 
                'size_charts': '📏 尺码表'
            };
            
            for (const [key, data] of Object.entries(stats)) {
                if (data.error) {
                    html += `
                        <div class="stat-card">
                            <h3>${collectionNames[key] || key}</h3>
                            <div class="error">错误: ${data.error}</div>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="stat-card">
                            <h3>${collectionNames[key] || key}</h3>
                            <div class="stat-number">${data.count || 0}</div>
                            <p>条记录</p>
                            ${data.sample_documents && data.sample_documents.length > 0 ? 
                                `<div style="margin-top: 15px;">
                                    <strong>样本数据:</strong><br>
                                    ${data.sample_documents.slice(0, 2).map(doc => 
                                        `<div style="background: white; padding: 8px; margin: 5px 0; border-radius: 4px; font-size: 0.9em;">${doc}</div>`
                                    ).join('')}
                                </div>` : ''
                            }
                        </div>
                    `;
                }
            }
            
            statsGrid.innerHTML = html;
        }

        // 搜索表单提交
        document.getElementById('searchForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const collection = document.getElementById('collection').value;
            const query = document.getElementById('query').value;
            const limit = document.getElementById('limit').value;
            
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div class="loading">搜索中...</div>';
            
            try {
                const response = await fetch(`/vector/search/${collection}?query=${encodeURIComponent(query)}&limit=${limit}`);
                const data = await response.json();
                
                if (data.success) {
                    displaySearchResults(data.data);
                } else {
                    resultsDiv.innerHTML = `<div class="error">搜索失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">搜索出错: ${error.message}</div>`;
            }
        });

        // 显示搜索结果
        function displaySearchResults(data) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (!data.results || data.results.length === 0) {
                resultsDiv.innerHTML = '<div class="error">未找到相关结果</div>';
                return;
            }
            
            let html = `<h3 style="margin-bottom: 20px;">🎯 搜索结果 (${data.results.length} 条)</h3>`;
            
            data.results.forEach((result, index) => {
                const score = (result.similarity_score * 100).toFixed(1);
                html += `
                    <div class="result-item">
                        <div class="result-header">
                            <strong>结果 ${index + 1}</strong>
                            <span class="similarity-score">${score}% 相似</span>
                        </div>
                        <div class="result-content">${result.content}</div>
                        <div class="result-meta">ID: ${result.id}</div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
