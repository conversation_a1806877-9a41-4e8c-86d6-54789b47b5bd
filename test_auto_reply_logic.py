#!/usr/bin/env python3
"""
测试自动回复逻辑修复
验证会话级别的自动回复控制是否正常工作
"""
import requests
import json
import time

def test_auto_reply_control():
    """测试自动回复控制逻辑"""
    base_url = "http://localhost:3002/api/v1"
    
    print("🔧 自动回复逻辑测试")
    print("=" * 50)
    
    # 测试用的会话ID（需要替换为实际存在的会话ID）
    conversation_id = "01985073-34f0-7931-9f7c-8fcf6ea509b6"
    
    print(f"测试会话ID: {conversation_id}")
    print("-" * 50)
    
    # 1. 获取当前自动回复状态
    print("1️⃣ 获取当前自动回复状态")
    try:
        response = requests.get(f"{base_url}/conversation-auto-reply/{conversation_id}/auto-reply")
        
        if response.status_code == 200:
            data = response.json()
            current_status = data.get('data', {}).get('autoReplyEnabled', False)
            print(f"   ✅ 当前状态: {'开启' if current_status else '关闭'}")
        else:
            print(f"   ❌ 获取状态失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return
    
    # 2. 测试关闭自动回复
    print("\n2️⃣ 测试关闭自动回复")
    try:
        response = requests.put(
            f"{base_url}/conversation-auto-reply/{conversation_id}/auto-reply",
            json={"enabled": False}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 关闭成功")
            print(f"   状态变化: {data.get('data', {}).get('changed', False)}")
        else:
            print(f"   ❌ 关闭失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 3. 验证关闭状态
    print("\n3️⃣ 验证关闭状态")
    try:
        response = requests.get(f"{base_url}/conversation-auto-reply/{conversation_id}/auto-reply")
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('data', {}).get('autoReplyEnabled', True)
            if not status:
                print(f"   ✅ 确认已关闭")
            else:
                print(f"   ❌ 状态异常，仍然开启")
        else:
            print(f"   ❌ 验证失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 4. 测试开启自动回复
    print("\n4️⃣ 测试开启自动回复")
    try:
        response = requests.put(
            f"{base_url}/conversation-auto-reply/{conversation_id}/auto-reply",
            json={"enabled": True}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 开启成功")
            print(f"   状态变化: {data.get('data', {}).get('changed', False)}")
        else:
            print(f"   ❌ 开启失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 5. 验证开启状态
    print("\n5️⃣ 验证开启状态")
    try:
        response = requests.get(f"{base_url}/conversation-auto-reply/{conversation_id}/auto-reply")
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('data', {}).get('autoReplyEnabled', False)
            if status:
                print(f"   ✅ 确认已开启")
            else:
                print(f"   ❌ 状态异常，仍然关闭")
        else:
            print(f"   ❌ 验证失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 6. 恢复原始状态
    print(f"\n6️⃣ 恢复原始状态: {'开启' if current_status else '关闭'}")
    try:
        response = requests.put(
            f"{base_url}/conversation-auto-reply/{conversation_id}/auto-reply",
            json={"enabled": current_status}
        )
        
        if response.status_code == 200:
            print(f"   ✅ 状态已恢复")
        else:
            print(f"   ❌ 恢复失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def test_environment_variables():
    """测试环境变量配置"""
    print("\n🌍 环境变量检查")
    print("-" * 30)
    
    # 检查 ykwy-assistant-api 的健康状态
    try:
        response = requests.get("http://localhost:3002/api/v1/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ ykwy-assistant-api 服务正常")
        else:
            print(f"❌ ykwy-assistant-api 服务异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ ykwy-assistant-api 连接失败: {e}")
    
    # 检查销售智能体服务
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 销售智能体服务正常")
            if 'available_brands' in data:
                print(f"   支持品牌: {data['available_brands']}")
        else:
            print(f"❌ 销售智能体服务异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 销售智能体连接失败: {e}")

def main():
    """主函数"""
    print("🔧 自动回复逻辑修复验证")
    print("=" * 60)
    
    print("📋 修复内容:")
    print("- 修复了只检查全局环境变量的问题")
    print("- 现在同时检查全局开关和会话级别开关")
    print("- 只有两个开关都为 true 才会触发自动回复")
    print("- 增加了详细的日志记录")
    
    # 环境检查
    test_environment_variables()
    
    # 自动回复控制测试
    test_auto_reply_control()
    
    print("\n📋 测试总结:")
    print("✅ 修复后的逻辑:")
    print("   1. 全局开关: SALES_AGENT_AUTO_REPLY=true")
    print("   2. 会话开关: conversation.autoReplyEnabled")
    print("   3. 最终条件: 全局开关 && 会话开关")
    print("\n🎯 现在每个会话都可以独立控制自动回复了！")

if __name__ == "__main__":
    main()
