"""
向量存储服务 - 基于ChromaDB的向量化查询功能
通过HTTP接口从ykwy-api获取数据并存储到向量数据库
"""
import os
import json
import logging
import requests
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

import chromadb
from chromadb.config import Settings
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core import Settings as LlamaSettings

logger = logging.getLogger(__name__)


@dataclass
class QueryResult:
    """查询结果数据类"""
    id: str
    content: str
    metadata: Dict[str, Any]
    score: float


class VectorStoreService:
    """向量存储服务类"""

    def __init__(self):
        from ..utils.config import settings

        # ChromaDB配置
        self.chroma_path = settings.chroma_db_path
        self.client = None
        self.collections = {}

        # 嵌入模型配置 - 使用兜底配置
        openai_api_key = os.getenv('OPENAI_API_KEY') or 'sk-VcvEXlVmU2QRwGfbtOfEUWgV4YE0W4bZhFiGucevMQF654usLJy4B9HLRU'
        openai_base_url = os.getenv('OPENAI_API_BASE') or os.getenv('OPENAI_BASE_URL') or 'https://api.wochirou.com/v1'

        self.embedding_model = OpenAIEmbedding(
            model="text-embedding-3-small",
            api_key=openai_api_key,
            base_url=openai_base_url
        )

        # 集合名称配置
        self.collection_names = {
            'products': 'ykwy_products',
            'qa_knowledge': 'ykwy_qa_knowledge'  # 保留集合，但通过Excel导入
        }

        logger.info("🔧 初始化向量存储服务")

    def initialize(self):
        """初始化ChromaDB客户端和集合"""
        try:
            # 创建ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=self.chroma_path,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )

            # 初始化所有集合
            for key, collection_name in self.collection_names.items():
                collection = self.client.get_or_create_collection(
                    name=collection_name,
                    metadata={"description": f"{key} vector collection"}
                )
                self.collections[key] = collection
                logger.info(f"✅ 初始化集合: {collection_name}")

            logger.info("✅ 向量存储服务初始化完成")

        except Exception as e:
            logger.error(f"❌ 初始化向量存储服务失败: {e}")
            raise

    def _make_api_request(self, endpoint: str, method: str = 'GET', data: Dict = None, max_retries: int = 3) -> Dict:
        """向ykwy-api发起HTTP请求，支持重试机制"""
        url = f"{self.api_base_url}{endpoint}"
        headers = {
            'Content-Type': 'application/json'
            # 'Authorization': f'Bearer {self.api_token}' if self.api_token else ''  # 临时注释掉
        }

        logger.info(f"🔗 请求URL: {url}")
        logger.info(f"📋 请求头: {headers}")

        last_error = None

        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 第 {attempt + 1}/{max_retries} 次尝试请求...")

                # 配置请求参数
                request_kwargs = {
                    'headers': headers,
                    'timeout': (10, 30),  # (连接超时, 读取超时)
                }

                if method == 'GET':
                    response = requests.get(url, **request_kwargs)
                elif method == 'POST':
                    request_kwargs['json'] = data
                    response = requests.post(url, **request_kwargs)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")

                logger.info(f"📊 响应状态码: {response.status_code}")
                logger.info(f"📄 响应内容类型: {response.headers.get('content-type', 'unknown')}")

                response.raise_for_status()

                # 添加调试信息
                response_text = response.text
                logger.info(f"📝 响应内容前200字符: {response_text[:200]}...")

                try:
                    json_data = response.json()
                    logger.info(f"📊 JSON数据类型: {type(json_data)}")
                    if isinstance(json_data, dict):
                        logger.info(f"📋 JSON键: {list(json_data.keys())}")
                    logger.info(f"✅ 请求成功 (第 {attempt + 1} 次尝试)")
                    return json_data
                except Exception as json_error:
                    logger.error(f"❌ JSON解析失败: {json_error}")
                    logger.error(f"📄 原始响应: {response_text}")
                    raise

            except requests.exceptions.ConnectionError as e:
                last_error = e
                logger.warning(f"⚠️ 连接错误 (第 {attempt + 1}/{max_retries} 次): {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避: 1s, 2s, 4s
                    logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue

            except requests.exceptions.Timeout as e:
                last_error = e
                logger.warning(f"⚠️ 请求超时 (第 {attempt + 1}/{max_retries} 次): {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue

            except requests.exceptions.RequestException as e:
                last_error = e
                logger.error(f"❌ 请求异常 (第 {attempt + 1}/{max_retries} 次): {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                break

        # 所有重试都失败了
        logger.error(f"❌ API请求最终失败 {endpoint}: {last_error}")
        raise last_error

    def _generate_embedding(self, text: str, max_retries: int = 3) -> List[float]:
        """生成文本嵌入向量（不使用零向量兜底）"""
        import time

        for attempt in range(max_retries):
            try:
                embedding = self.embedding_model.get_text_embedding(text)
                if embedding and len(embedding) > 0:
                    # 检查是否为零向量
                    if all(x == 0.0 for x in embedding):
                        raise ValueError("API返回了零向量")
                    return embedding
                else:
                    raise ValueError("API返回了空向量")

            except Exception as e:
                logger.error(f"❌ 生成嵌入向量失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间：2s, 4s, 6s
                    logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"💥 生成嵌入向量最终失败，已重试 {max_retries} 次")
                    raise Exception(f"无法生成嵌入向量: {e}")

        raise Exception("生成嵌入向量失败")

    def sync_products(self, shop_id: str, incremental: bool = True) -> int:
        """
        从API同步产品数据到向量存储

        Args:
            shop_id: 店铺ID
            incremental: 是否增量同步，True=只同步新数据，False=全量同步

        Returns:
            同步的产品数量
        """
        import time
        import requests

        start_time = time.time()
        sync_type = "增量" if incremental else "全量"
        logger.info(f"🚀 开始{sync_type}同步产品数据 (店铺: {shop_id})...")

        try:
            if not self.client:
                self.initialize()

            # 调用新的产品API (先尝试分页，如果不支持则使用原方式)
            from ..utils.config import settings
            api_url = f"{settings.ykwy_api_base_url}/api/v2/temp-product/search"
            headers = {"Access-Token": settings.ykwy_api_token}

            logger.info(f"📡 调用产品API: {api_url}")

            # 尝试分页查询
            try:
                page = 1
                page_size = 100  # 每页100个产品
                all_products = []

                while True:
                    params = {
                        "shopId": shop_id,
                        "page": page,
                        "pageSize": page_size
                    }

                    logger.info(f"📋 请求第 {page} 页产品数据 (每页 {page_size} 个)")

                    response = requests.get(api_url, params=params, headers=headers, timeout=30)
                    response.raise_for_status()

                    api_data = response.json()
                    if api_data.get("code") != 0:
                        raise Exception(f"API返回错误: {api_data.get('message', '未知错误')}")

                    # 检查是否是分页格式
                    if isinstance(api_data.get("data"), dict) and "data" in api_data["data"]:
                        # 分页格式
                        page_data = api_data["data"]
                        products = page_data.get("data", [])
                        total = page_data.get("total", 0)
                        total_pages = page_data.get("totalPages", 0)

                        logger.info(f"📊 第 {page} 页获取到 {len(products)} 个产品 (总计: {total} 个, 共 {total_pages} 页)")

                        if not products:
                            break

                        all_products.extend(products)

                        if page >= total_pages:
                            break

                        page += 1
                    else:
                        # 非分页格式，直接返回所有数据
                        all_products = api_data.get("data", [])
                        logger.info(f"📊 获取到 {len(all_products)} 个产品 (非分页模式)")
                        break

                products = all_products

            except Exception as e:
                # 如果分页查询失败，尝试原始方式
                logger.warning(f"⚠️ 分页查询失败，尝试原始方式: {e}")
                params = {"shopId": shop_id}
                response = requests.get(api_url, params=params, headers=headers, timeout=30)
                response.raise_for_status()

                api_data = response.json()
                if api_data.get("code") != 0:
                    raise Exception(f"API返回错误: {api_data.get('message', '未知错误')}")

                products = api_data.get("data", [])
                logger.info(f"📊 从API获取到 {len(products)} 个产品 (原始模式)")

            if not products:
                logger.warning("⚠️ 未获取到产品数据")
                return 0

            # 处理现有数据
            collection = self.collections['products']
            existing_ids = set()

            if not incremental:
                # 全量同步：清空现有数据
                try:
                    existing_data = collection.get()
                    if existing_data['ids']:
                        collection.delete(ids=existing_data['ids'])
                        logger.info(f"🗑️ 清空了 {len(existing_data['ids'])} 条现有产品数据")
                except Exception as e:
                    logger.warning(f"⚠️ 清空产品数据时出现警告: {e}")
            else:
                # 增量同步：获取现有ID列表
                try:
                    existing_data = collection.get()
                    if existing_data['ids']:
                        existing_ids = set(existing_data['ids'])
                        logger.info(f"📊 现有产品记录: {len(existing_ids)} 条，将进行增量同步")
                except Exception as e:
                    logger.warning(f"⚠️ 获取现有数据时出现警告: {e}")

            # 批量处理产品数据
            batch_size = 10
            total_processed = 0
            total_skipped = 0

            for i in range(0, len(products), batch_size):
                batch = products[i:i + batch_size]
                processed, skipped = self._process_product_batch(
                    collection, batch, existing_ids, shop_id
                )
                total_processed += processed
                total_skipped += skipped

                logger.info(f"✅ 已处理 {total_processed} 个产品 (跳过: {total_skipped})")

            elapsed_time = time.time() - start_time
            logger.info(f"🎉 {sync_type}同步产品数据完成！")
            logger.info(f"📊 新增产品: {total_processed} 个")
            logger.info(f"📊 跳过重复: {total_skipped} 个")
            logger.info(f"📊 总计产品: {len(existing_ids) + total_processed} 个")
            logger.info(f"⏱️ 耗时: {elapsed_time:.2f} 秒")

            return total_processed

        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ {sync_type}同步产品数据失败: {e} (耗时 {elapsed_time:.2f} 秒)")
            import traceback
            logger.error(f"❌ 错误堆栈: {traceback.format_exc()}")
            raise

    def _process_product_batch(self, collection, batch_products, existing_ids, shop_id):
        """处理新API产品数据批次"""
        try:
            processed_count = 0
            skipped_count = 0

            ids = []
            documents = []
            metadatas = []
            embeddings = []

            for product in batch_products:
                try:
                    # 使用产品ID作为向量ID
                    product_id = str(product.get('id', ''))
                    if not product_id:
                        logger.warning("⚠️ 产品缺少ID，跳过")
                        skipped_count += 1
                        continue

                    # 检查是否已存在（增量同步）
                    if product_id in existing_ids:
                        skipped_count += 1
                        continue

                    # 格式化产品内容用于向量化
                    content = self._format_product_content(product)

                    # 生成嵌入向量
                    embedding = self._generate_embedding(content, max_retries=3)

                    # 准备元数据 - 确保所有值都不是None
                    metadata = {
                        'type': 'product',
                        'source': 'api',
                        'shop_id': str(shop_id or ''),
                        'brand_id': 'qinggong',  # 轻功体育
                        'product_id': str(product.get('productId') or ''),
                        'name': str(product.get('name') or ''),
                        'status': str(product.get('status') or ''),
                        'style_number': str(product.get('styleNumber') or ''),
                        'image_url': str(product.get('imageUrl') or ''),
                        'link_or_id': str(product.get('linkOrId') or ''),
                        'created_at': str(product.get('createdAt') or ''),
                        'updated_at': str(product.get('updatedAt') or '')
                    }

                    ids.append(product_id)
                    documents.append(content)
                    metadatas.append(metadata)
                    embeddings.append(embedding)
                    processed_count += 1

                except Exception as e:
                    logger.error(f"❌ 处理产品失败: {product.get('name', 'unknown')} - {e}")
                    skipped_count += 1
                    continue

            # 批量添加到向量存储
            if ids:
                collection.add(
                    ids=ids,
                    documents=documents,
                    metadatas=metadatas,
                    embeddings=embeddings
                )
                logger.info(f"✅ 批次处理成功: {processed_count} 个产品")

            return processed_count, skipped_count

        except Exception as e:
            logger.error(f"❌ 批次处理失败: {e}")
            return 0, len(batch_products)





    def import_qa_from_excel(self, excel_path: str, clear_existing: bool = True, batch_size: int = 50) -> int:
        """从Excel文件导入问答知识库数据到向量存储（分离向量化 + 分批处理）

        Args:
            excel_path: Excel文件路径
            clear_existing: 是否清空现有数据，默认为True
            batch_size: 批处理大小，默认50个向量一批

        Returns:
            导入的向量数量
        """
        logger.info(f"📊 开始从Excel导入问答知识库数据: {excel_path}")
        logger.info(f"🔄 使用分离向量化策略，批处理大小: {batch_size}")

        try:
            from openpyxl import load_workbook

            # 读取Excel文件
            workbook = load_workbook(excel_path)
            sheet = workbook.active

            # 获取数据范围
            max_row = sheet.max_row
            max_col = sheet.max_column
            logger.info(f"📄 Excel文件读取成功，共 {max_row-1} 行数据")

            # 读取表头
            headers = []
            for col in range(1, max_col + 1):
                cell_value = sheet.cell(row=1, column=col).value
                headers.append(str(cell_value) if cell_value else f"Column_{col}")

            # 查找问题和答案列
            question_col_idx = None
            answer_col_idx = None

            for i, header in enumerate(headers):
                header_lower = header.lower()
                if any(keyword in header_lower for keyword in ['问题', 'question', '问', 'q']):
                    question_col_idx = i + 1
                elif any(keyword in header_lower for keyword in ['答案', 'answer', '答', 'a', '回答']):
                    answer_col_idx = i + 1

            if not question_col_idx or not answer_col_idx:
                # 如果找不到，使用前两列
                question_col_idx = 1
                answer_col_idx = 2
                logger.warning("⚠️ 未找到明确的问题/答案列，使用前两列")

            logger.info(f"📋 问题列: {headers[question_col_idx-1]}")
            logger.info(f"📋 答案列: {headers[answer_col_idx-1]}")

            collection = self.collections['qa_knowledge']

            # 处理现有数据
            existing_ids = set()
            if clear_existing:
                try:
                    existing_data = collection.get()
                    if existing_data['ids']:
                        collection.delete(ids=existing_data['ids'])
                        logger.info(f"🗑️ 清空了 {len(existing_data['ids'])} 条现有问答数据")
                except Exception as e:
                    logger.warning(f"⚠️ 清空问答数据时出现警告: {e}")
            else:
                # 增量导入：获取现有ID列表
                try:
                    existing_data = collection.get()
                    if existing_data['ids']:
                        existing_ids = set(existing_data['ids'])
                        logger.info(f"📊 现有问答记录: {len(existing_ids)} 条，将进行增量导入")
                except Exception as e:
                    logger.warning(f"⚠️ 获取现有数据时出现警告: {e}")

            # 分批处理问答数据
            total_vectors = 0
            total_skipped = 0
            current_batch = []

            for row_idx in range(2, max_row + 1):  # 跳过表头
                question_cell = sheet.cell(row=row_idx, column=question_col_idx).value
                answer_cell = sheet.cell(row=row_idx, column=answer_col_idx).value

                # 跳过空行
                if not question_cell or not answer_cell:
                    continue

                # 解析问题变体（JSON格式）
                question_variants = []
                try:
                    if isinstance(question_cell, str) and (question_cell.startswith('[') or question_cell.startswith('{')):
                        parsed_questions = json.loads(question_cell)
                        if isinstance(parsed_questions, list):
                            question_variants = [str(q).strip() for q in parsed_questions if q]
                        else:
                            question_variants = [str(question_cell).strip()]
                    else:
                        question_variants = [str(question_cell).strip()]
                except json.JSONDecodeError:
                    question_variants = [str(question_cell).strip()]

                answer = str(answer_cell).strip()

                if not question_variants or not answer:
                    continue

                # 生成问答组ID
                qa_group_id = f"qa_group_{row_idx-1}"

                # 为每个问题变体创建向量数据
                for variant_idx, question_variant in enumerate(question_variants, 1):
                    if not question_variant:
                        continue

                    vector_id = f"{qa_group_id}_variant_{variant_idx}"

                    # 增量导入：检查是否已存在
                    if vector_id in existing_ids:
                        total_skipped += 1
                        continue

                    vector_data = {
                        'id': vector_id,
                        'content': question_variant,  # 只向量化问题
                        'metadata': {
                            'type': 'qa_knowledge',
                            'source': 'excel',
                            'qa_group_id': qa_group_id,
                            'question': question_variant,
                            'answer': answer,
                            'variant_index': variant_idx,
                            'total_variants': len(question_variants),
                            'row_index': row_idx
                        }
                    }

                    current_batch.append(vector_data)

                    # 当批次满了或者是最后一行时，处理这个批次
                    if len(current_batch) >= batch_size or row_idx == max_row:
                        if current_batch:  # 只有当批次不为空时才处理
                            try:
                                success_count = self._process_qa_batch(collection, current_batch)
                                total_vectors += success_count

                                logger.info(f"✅ 已处理 {total_vectors} 个新向量 (当前批次: {success_count}, 跳过: {total_skipped})")
                            except Exception as e:
                                logger.error(f"💥 批次处理失败，终止导入: {e}")
                                raise  # 重新抛出异常，终止整个导入过程
                        current_batch = []  # 清空当前批次

            # 处理剩余的批次
            if current_batch:
                try:
                    success_count = self._process_qa_batch(collection, current_batch)
                    total_vectors += success_count
                    logger.info(f"✅ 处理最后批次: {success_count} 个新向量")
                except Exception as e:
                    logger.error(f"💥 最后批次处理失败，终止导入: {e}")
                    raise

            logger.info(f"🎉 增量导入完成！")
            logger.info(f"📊 新增向量: {total_vectors} 个")
            logger.info(f"📊 跳过重复: {total_skipped} 个")
            logger.info(f"📊 总计向量: {len(existing_ids) + total_vectors} 个")
            return total_vectors

        except ImportError:
            logger.error("❌ 需要安装openpyxl库: poetry add openpyxl")
            raise
        except Exception as e:
            logger.error(f"❌ 从Excel导入问答数据失败: {e}")
            import traceback
            logger.error(f"❌ 错误堆栈: {traceback.format_exc()}")
            raise

    def _process_qa_batch(self, collection, batch_data: List[Dict]) -> int:
        """处理一个问答批次的数据

        Args:
            collection: ChromaDB集合
            batch_data: 批次数据列表

        Returns:
            成功处理的向量数量
        """
        try:
            if not batch_data:
                return 0

            logger.info(f"🔄 处理问答批次: {len(batch_data)} 个向量...")

            ids = []
            documents = []
            metadatas = []
            embeddings = []
            failed_count = 0

            for i, vector_data in enumerate(batch_data):
                try:
                    # 生成嵌入向量（不使用零向量兜底）
                    embedding = self._generate_embedding(vector_data['content'], max_retries=3)

                    ids.append(vector_data['id'])
                    documents.append(vector_data['content'])
                    metadatas.append(vector_data['metadata'])
                    embeddings.append(embedding)

                except Exception as e:
                    failed_count += 1
                    question = vector_data['metadata'].get('question', 'unknown')
                    logger.error(f"❌ 向量 {i+1} 生成失败: {question} - {e}")

                    # 如果失败太多，终止整个批次
                    if failed_count > len(batch_data) * 0.5:  # 超过50%失败
                        logger.error(f"💥 批次失败率过高 ({failed_count}/{len(batch_data)})，终止处理")
                        raise Exception(f"批次失败率过高: {failed_count}/{len(batch_data)}")

            # 只有成功的向量才添加到数据库
            if ids:
                collection.add(
                    ids=ids,
                    documents=documents,
                    metadatas=metadatas,
                    embeddings=embeddings
                )

                logger.info(f"✅ 批次处理成功: {len(ids)} 个向量")
                if failed_count > 0:
                    logger.warning(f"⚠️ 批次中有 {failed_count} 个向量生成失败")
            else:
                logger.error("❌ 批次中没有成功的向量")

            return len(ids)

        except Exception as e:
            logger.error(f"❌ 批次处理失败: {e}")
            # 不返回0，而是抛出异常，让上层决定如何处理
            raise





    def _format_product_content(self, product) -> str:
        """格式化产品内容用于向量化"""
        # 添加类型检查
        if isinstance(product, str):
            logger.warning(f"⚠️ 产品数据是字符串类型: {product[:100]}...")
            return f"产品信息: {product}"

        if not isinstance(product, dict):
            logger.warning(f"⚠️ 产品数据类型异常: {type(product)}")
            return f"产品信息: {str(product)}"

        parts = [
            f"商品名称: {product.get('name', '')}",
            f"商品ID: {product.get('productId', '')}",
            f"商品状态: {product.get('status', '')}"
        ]

        if product.get('styleNumber'):
            parts.append(f"货号款号: {product['styleNumber']}")

        if product.get('description'):
            try:
                desc = json.loads(product['description']) if isinstance(product['description'], str) else product['description']
                parts.append(f"商品描述: {json.dumps(desc, ensure_ascii=False)}")
            except:
                parts.append(f"商品描述: {product['description']}")

        if product.get('linkOrId'):
            parts.append(f"商品链接: {product['linkOrId']}")

        return '\n'.join(parts)

    def sync_qa_from_api(self, shop_id: str, incremental: bool = True) -> int:
        """
        从API同步问答知识库数据到向量存储

        Args:
            shop_id: 店铺ID
            incremental: 是否增量同步，True=只同步新数据，False=全量同步

        Returns:
            同步的问答数量
        """
        import time
        import requests

        start_time = time.time()
        sync_type = "增量" if incremental else "全量"
        logger.info(f"🚀 开始{sync_type}同步问答知识库数据 (店铺: {shop_id})...")

        try:
            if not self.client:
                self.initialize()

            # 分页获取问答知识库数据
            from ..utils.config import settings
            api_url = f"{settings.ykwy_api_base_url}/api/v2/qa-knowledge-base/by-shop"
            headers = {"Access-Token": settings.ykwy_api_token}

            logger.info(f"📡 调用问答知识库API: {api_url}")

            # 分页参数
            page = 1
            page_size = 50  # 每页50条数据
            all_qa_items = []

            while True:
                params = {
                    "shopId": shop_id,
                    "page": page,
                    "pageSize": page_size
                }

                logger.info(f"📋 请求第 {page} 页数据 (每页 {page_size} 条)")

                response = requests.get(api_url, params=params, headers=headers, timeout=30)
                response.raise_for_status()

                api_data = response.json()
                if api_data.get("code") != 0:
                    raise Exception(f"API返回错误: {api_data.get('message', '未知错误')}")

                # 解析分页数据
                page_data = api_data.get("data", {})
                qa_items = page_data.get("data", [])
                total = page_data.get("total", 0)
                total_pages = page_data.get("totalPages", 0)

                logger.info(f"📊 第 {page} 页获取到 {len(qa_items)} 条数据 (总计: {total} 条, 共 {total_pages} 页)")

                if not qa_items:
                    break

                all_qa_items.extend(qa_items)

                # 检查是否还有更多页
                if page >= total_pages:
                    break

                page += 1

            logger.info(f"📊 分页查询完成，共获取到 {len(all_qa_items)} 条问答知识库数据")

            if not all_qa_items:
                logger.warning("⚠️ 未获取到问答知识库数据")
                return 0

            # 处理现有数据
            collection = self.collections['qa_knowledge']
            existing_ids = set()

            if not incremental:
                # 全量同步：清空现有数据
                try:
                    existing_data = collection.get()
                    if existing_data['ids']:
                        collection.delete(ids=existing_data['ids'])
                        logger.info(f"🗑️ 清空了 {len(existing_data['ids'])} 条现有问答数据")
                except Exception as e:
                    logger.warning(f"⚠️ 清空问答数据时出现警告: {e}")
            else:
                # 增量同步：获取现有ID列表
                try:
                    existing_data = collection.get()
                    if existing_data['ids']:
                        existing_ids = set(existing_data['ids'])
                        logger.info(f"📊 现有问答记录: {len(existing_ids)} 条，将进行增量同步")
                except Exception as e:
                    logger.warning(f"⚠️ 获取现有数据时出现警告: {e}")

            # 分批处理问答数据
            batch_size = 10
            total_vectors = 0
            total_skipped = 0
            current_batch = []

            for qa_item in all_qa_items:
                qa_id = qa_item.get('id')
                if not qa_id:
                    logger.warning("⚠️ 问答项缺少ID，跳过")
                    continue

                # 获取问题样例和答案
                question_samples = qa_item.get('commonQuestionSamples', [])
                answers = qa_item.get('answers', [])

                if not question_samples or not answers:
                    logger.warning(f"⚠️ 问答项 {qa_id} 缺少问题样例或答案，跳过")
                    continue

                # 合并所有答案内容
                answer_content = "\n".join([a.get('content', '') for a in answers if a.get('content')])
                if not answer_content:
                    logger.warning(f"⚠️ 问答项 {qa_id} 没有有效答案内容，跳过")
                    continue

                # 为每个问题样例创建向量
                for variant_idx, question in enumerate(question_samples, 1):
                    if not question:
                        continue

                    vector_id = f"{qa_id}_variant_{variant_idx}"

                    # 增量导入：检查是否已存在
                    if vector_id in existing_ids:
                        total_skipped += 1
                        continue

                    vector_data = {
                        'id': vector_id,
                        'content': question,  # 只向量化问题
                        'metadata': {
                            'type': 'qa_knowledge',
                            'source': 'api',
                            'qa_group_id': qa_id,
                            'question': question,
                            'answer': answer_content,
                            'variant_index': variant_idx,
                            'total_variants': len(question_samples),
                            'question_type': qa_item.get('questionType', ''),
                            'category_code': qa_item.get('categoryCode', ''),
                            'category_path': qa_item.get('categoryPath', ''),
                            'shop_id': shop_id,
                            'brand_id': 'qinggong'  # 轻功体育
                        }
                    }

                    current_batch.append(vector_data)

                    # 当批次满了时，处理这个批次
                    if len(current_batch) >= batch_size:
                        try:
                            success_count = self._process_qa_batch(collection, current_batch)
                            total_vectors += success_count

                            logger.info(f"✅ 已处理 {total_vectors} 个新向量 (当前批次: {success_count}, 跳过: {total_skipped})")
                        except Exception as e:
                            logger.error(f"💥 批次处理失败，终止导入: {e}")
                            raise
                        current_batch = []  # 清空当前批次

            # 处理剩余的批次
            if current_batch:
                try:
                    success_count = self._process_qa_batch(collection, current_batch)
                    total_vectors += success_count
                    logger.info(f"✅ 处理最后批次: {success_count} 个新向量")
                except Exception as e:
                    logger.error(f"💥 最后批次处理失败，终止导入: {e}")
                    raise

            elapsed_time = time.time() - start_time
            logger.info(f"🎉 {sync_type}同步问答知识库完成！")
            logger.info(f"📊 新增向量: {total_vectors} 个")
            logger.info(f"📊 跳过重复: {total_skipped} 个")
            logger.info(f"📊 总计向量: {len(existing_ids) + total_vectors} 个")
            logger.info(f"⏱️ 耗时: {elapsed_time:.2f} 秒")

            return total_vectors

        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ {sync_type}同步问答知识库失败: {e} (耗时 {elapsed_time:.2f} 秒)")
            import traceback
            logger.error(f"❌ 错误堆栈: {traceback.format_exc()}")
            raise

    def _format_qa_content(self, qa: Dict) -> str:
        """格式化问答内容用于向量化 - 适配 questionAndAnswerKnowledgeBase 表结构"""
        parts = [
            f"问题类型: {qa.get('questionType', '')}",
            f"订单状态: {qa.get('orderStatus', '')}",
            f"分类编码: {qa.get('categoryCode', '')}"
        ]

        # 处理常见问法样本
        if qa.get('commonQuestionSamples'):
            samples = qa['commonQuestionSamples']
            if isinstance(samples, list) and samples:
                parts.append(f"常见问法: {'; '.join(samples)}")

        # 处理答案 - questionAndAnswerKnowledgeBase 表中答案是对象数组
        if qa.get('answers'):
            answers = []
            for answer in qa['answers']:
                if isinstance(answer, dict) and answer.get('content'):
                    answers.append(answer['content'])
                elif isinstance(answer, str):
                    answers.append(answer)
            if answers:
                parts.append(f"答案: {'; '.join(answers)}")

        # 处理匹配模式
        if qa.get('matchMode'):
            parts.append(f"匹配模式: {qa['matchMode']}")

        # 处理商品相关信息
        if qa.get('product') and isinstance(qa['product'], dict):
            product_name = qa['product'].get('name', '')
            if product_name:
                parts.append(f"商品名称: {product_name}")

        # 处理店铺信息
        if qa.get('Shop') and isinstance(qa['Shop'], dict):
            shop_name = qa['Shop'].get('name', '')
            if shop_name:
                parts.append(f"店铺名称: {shop_name}")

        return '\n'.join(parts)



    def search_products(self, query: str, limit: int = 5, filters: Dict = None) -> List[QueryResult]:
        """搜索产品"""
        # 记录输入参数
        logger.info(f"🔧 VectorStoreService.search_products 调用")
        logger.info(f"📥 输入参数: query='{query}', limit={limit}, filters={filters}")

        try:
            collection = self.collections['products']
            query_embedding = self._generate_embedding(query)

            # 构建 ChromaDB 兼容的过滤器
            where_clause = None
            if filters:
                if len(filters) == 1:
                    # 单个过滤条件
                    key, value = next(iter(filters.items()))
                    where_clause = {key: {"$eq": value}}
                else:
                    # 多个过滤条件，使用 $and 操作符
                    conditions = []
                    for key, value in filters.items():
                        conditions.append({key: {"$eq": value}})
                    where_clause = {"$and": conditions}

                logger.info(f"🔍 使用过滤条件: {where_clause}")

            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=limit,
                where=where_clause
            )

            return self._format_query_results(results)

        except Exception as e:
            logger.error(f"❌ 搜索产品失败: {e}")
            return []

    def search_qa_knowledge(self, query: str, limit: int = 5, filters: Dict = None) -> List[QueryResult]:
        """搜索问答知识库（支持分离向量化的去重）"""
        try:
            logger.info(f"🔍 搜索问答知识库: {query}")

            collection = self.collections['qa_knowledge']
            query_embedding = self._generate_embedding(query)

            # 构建 ChromaDB 兼容的过滤器
            where_clause = None
            if filters:
                if len(filters) == 1:
                    key, value = next(iter(filters.items()))
                    where_clause = {key: {"$eq": value}}
                else:
                    conditions = []
                    for key, value in filters.items():
                        conditions.append({key: {"$eq": value}})
                    where_clause = {"$and": conditions}

            # 获取更多结果以便去重
            search_limit = limit * 3
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=search_limit,
                where=where_clause
            )

            # 格式化所有结果
            all_results = self._format_query_results(results)

            # 去重：每个问答组只保留最高分的变体
            qa_groups = {}
            for result in all_results:
                qa_group_id = result.metadata.get('qa_group_id', result.id)

                if qa_group_id not in qa_groups or result.score > qa_groups[qa_group_id].score:
                    qa_groups[qa_group_id] = result

            # 按分数排序并返回指定数量
            unique_results = list(qa_groups.values())
            unique_results.sort(key=lambda x: x.score, reverse=True)

            final_results = unique_results[:limit]

            logger.info(f"✅ 找到 {len(all_results)} 条原始结果，去重后 {len(final_results)} 条问答知识")
            return final_results

        except Exception as e:
            logger.error(f"❌ 搜索问答知识库失败: {e}")
            return []



    def _format_query_results(self, results) -> List[QueryResult]:
        """格式化查询结果"""
        query_results = []

        if results['ids'] and results['ids'][0]:
            for i in range(len(results['ids'][0])):
                query_results.append(QueryResult(
                    id=results['ids'][0][i],
                    content=results['documents'][0][i],
                    metadata=results['metadatas'][0][i],
                    score=1 - results['distances'][0][i] if results.get('distances') else 0.0
                ))

        return query_results

    def intelligent_qa(self, question: str, shop_id: str = None) -> Dict:
        """智能问答 - 综合搜索所有数据源"""
        try:
            # 构建过滤条件
            filters = {'shop_id': shop_id} if shop_id else None

            # 搜索产品和问答知识库
            products = self.search_products(question, 3, filters)
            qa_knowledge = self.search_qa_knowledge(question, 5, filters)

            total_results = len(products) + len(qa_knowledge)

            # 生成智能建议
            suggestion = ""
            if qa_knowledge:
                suggestion = "建议优先参考问答知识库中的标准答案。"
            elif products:
                suggestion = "找到了相关产品信息，可以为客户提供详细介绍。"
            else:
                suggestion = "未找到直接相关的信息，建议使用通用回复或转人工客服。"

            return {
                'success': True,
                'data': {
                    'products': [self._result_to_dict(r) for r in products],
                    'qa_knowledge': [self._result_to_dict(r) for r in qa_knowledge]
                },
                'message': f"综合搜索完成，共找到 {total_results} 条相关信息",
                'suggestion': suggestion
            }

        except Exception as e:
            logger.error(f"❌ 智能问答失败: {e}")
            return {
                'success': False,
                'data': {'products': [], 'qa_knowledge': []},
                'message': f"智能问答失败: {str(e)}",
                'suggestion': "系统出现问题，建议转人工客服处理。"
            }

    def _result_to_dict(self, result: QueryResult) -> Dict:
        """将QueryResult转换为字典"""
        return {
            'id': result.id,
            'content': result.content,
            'metadata': result.metadata,
            'score': result.score
        }



    def health_check(self) -> Dict:
        """健康检查"""
        try:
            if not self.client:
                return {
                    'status': 'unhealthy',
                    'message': '向量存储未初始化',
                    'collections': {}
                }

            collections_status = {}
            for key, collection in self.collections.items():
                try:
                    count = collection.count()
                    collections_status[key] = count
                except Exception as e:
                    collections_status[key] = f"error: {str(e)}"

            return {
                'status': 'healthy',
                'message': '向量存储服务正常',
                'collections': collections_status
            }

        except Exception as e:
            logger.error(f"❌ 向量存储健康检查失败: {e}")
            return {
                'status': 'unhealthy',
                'message': f'健康检查失败: {str(e)}',
                'collections': {}
            }

    def get_vector_statistics(self) -> Dict:
        """获取向量数据统计信息"""
        logger.info("🔧 开始获取向量数据统计信息")
        try:
            if not self.client:
                self.initialize()

            stats = {}
            logger.info(f"📊 检查 {len(self.collections)} 个集合: {list(self.collections.keys())}")

            for key, collection in self.collections.items():
                try:
                    logger.info(f"🔍 检查集合 {key}...")
                    # 获取集合基本信息
                    count = collection.count()
                    logger.info(f"📊 集合 {key} 记录数: {count}")

                    # 获取样本数据
                    sample_data = collection.get(limit=5)
                    logger.info(f"📊 集合 {key} 样本数据: {len(sample_data.get('documents', []))} 条")

                    # 统计元数据分布
                    metadata_stats = {}
                    if sample_data['metadatas']:
                        for metadata in sample_data['metadatas']:
                            for field, value in metadata.items():
                                if field not in metadata_stats:
                                    metadata_stats[field] = {}
                                value_str = str(value)
                                metadata_stats[field][value_str] = metadata_stats[field].get(value_str, 0) + 1

                    stats[key] = {
                        'count': count,
                        'sample_ids': sample_data['ids'][:3] if sample_data['ids'] else [],
                        'sample_documents': [doc[:100] + '...' if len(doc) > 100 else doc
                                           for doc in (sample_data['documents'][:3] if sample_data['documents'] else [])],
                        'metadata_fields': list(metadata_stats.keys()),
                        'metadata_distribution': metadata_stats
                    }

                except Exception as e:
                    stats[key] = {'error': str(e)}

            return {
                'success': True,
                'data': stats,
                'message': '向量数据统计获取成功'
            }

        except Exception as e:
            logger.error(f"❌ 获取向量统计失败: {e}")
            return {
                'success': False,
                'message': f'获取向量统计失败: {str(e)}',
                'data': {}
            }

    def search_similar_vectors(self, collection_name: str, query: str, limit: int = 10) -> Dict:
        """搜索相似向量并返回详细信息"""
        try:
            if collection_name not in self.collections:
                return {
                    'success': False,
                    'message': f'集合 {collection_name} 不存在',
                    'data': []
                }

            collection = self.collections[collection_name]
            query_embedding = self._generate_embedding(query)

            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=limit,
                include=['documents', 'metadatas', 'distances']
            )

            # 格式化结果
            formatted_results = []
            if results['ids'] and results['ids'][0]:
                for i in range(len(results['ids'][0])):
                    formatted_results.append({
                        'id': results['ids'][0][i],
                        'content': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'similarity_score': 1 - results['distances'][0][i] if results.get('distances') else 0.0,
                        'distance': results['distances'][0][i] if results.get('distances') else 0.0
                    })

            return {
                'success': True,
                'data': {
                    'query': query,
                    'collection': collection_name,
                    'results': formatted_results,
                    'total_found': len(formatted_results)
                },
                'message': f'在 {collection_name} 中找到 {len(formatted_results)} 条相似记录'
            }

        except Exception as e:
            logger.error(f"❌ 搜索相似向量失败: {e}")
            return {
                'success': False,
                'message': f'搜索失败: {str(e)}',
                'data': []
            }
