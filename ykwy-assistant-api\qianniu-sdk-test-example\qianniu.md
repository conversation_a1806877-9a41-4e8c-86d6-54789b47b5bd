## 获取商品(encryptId可以随意填)

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.item.search',
  param: {
    "pageSize": 8,
    "pageNo": 1,
    "keyWord": "",
    "sortKey": "sold",
    "desc": true,
    "type": 0,
    "queryGift": false,
    "encryptId": "RAzN8BQuL2aMkkVeK6SGe3CZHv7eb"
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.item.search",
    "data": {
        "data": [
            {
                "approveStatus": "onsale",
                "buttons": [
                    {
                        "behavior": "NORMAL",
                        "code": "sendItem",
                        "hidden": false,
                        "name": "发送宝贝",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "inviteBuy",
                        "hidden": false,
                        "name": "邀请下单",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "specialDiscount",
                        "hidden": false,
                        "name": "专属优惠",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "discountCalc",
                        "hidden": false,
                        "name": "优惠计算",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "collect",
                        "hidden": false,
                        "name": "收藏",
                        "type": "official"
                    }
                ],
                "categoryId": 202062620,
                "haveGift": false,
                "itemId": 953398286214,
                "itemUrl": "https://item.taobao.com/item.htm?id=953398286214",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01KXoJMu27PQ1yznqHB_!!**********.png",
                "price": "0.20",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 9,
                "soldQuantity": 1,
                "tags": "tags:25282,3137538,2943746,2902850,3326722,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本"
            },
            {
                "approveStatus": "onsale",
                "buttons": [
                    {
                        "behavior": "NORMAL",
                        "code": "sendItem",
                        "hidden": false,
                        "name": "发送宝贝",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "inviteBuy",
                        "hidden": false,
                        "name": "邀请下单",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "specialDiscount",
                        "hidden": false,
                        "name": "专属优惠",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "discountCalc",
                        "hidden": false,
                        "name": "优惠计算",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "collect",
                        "hidden": false,
                        "name": "收藏",
                        "type": "official"
                    }
                ],
                "categoryId": 202062620,
                "haveGift": false,
                "itemId": ************,
                "itemUrl": "https://item.taobao.com/item.htm?id=************",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                "price": "0.10",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 10,
                "soldQuantity": 0,
                "tags": "tags:3203330,3203458,3203266,3203394,3185538,25282,3215426,3213250,3137538,2943746,2902850,1904386",
                "title": "AI 智能写作助手｜高效产出各类文案"
            },
            {
                "approveStatus": "onsale",
                "buttons": [
                    {
                        "behavior": "NORMAL",
                        "code": "sendItem",
                        "hidden": false,
                        "name": "发送宝贝",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "inviteBuy",
                        "hidden": false,
                        "name": "邀请下单",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "specialDiscount",
                        "hidden": false,
                        "name": "专属优惠",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "discountCalc",
                        "hidden": false,
                        "name": "优惠计算",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "collect",
                        "hidden": false,
                        "name": "收藏",
                        "type": "official"
                    }
                ],
                "categoryId": 202062620,
                "haveGift": false,
                "itemId": 947227733604,
                "itemUrl": "https://item.taobao.com/item.htm?id=947227733604",
                "pic": "https://img.alicdn.com/bao/uploaded/i1/**********/O1CN01MMO9Xx27PQ1gJzaEO_!!**********.png",
                "price": "0.10",
                "props": "20000:30025069481;20435:6076017",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值",
                "quantity": 10,
                "soldQuantity": 0,
                "tags": "tags:25282,3137538,2943746,2902850,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3614146,3436226,3444226,2016386,1904386",
                "title": "AI 写作服务 智能创作 商业文案代写产品详情页营销策划 定制服务"
            }
        ],
        "page": 1,
        "pageSize": 8,
        "totalCount": 3
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 查询买家id

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.airisland.contact.search',
  param: {
    "accessKey": "qianniu-pc",
    "accessSecret": "qianniu-pc-secret",
    "accountType": "3",
    "searchQuery": "tb783904683"
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.airisland.contact.search",
    "data": {
        "code": "0",
        "data": [
            {
                "accountId": "*************",
                "accountRoles": [
                    "buyer"
                ],
                "accountType": 3,
                "bizType": "-1",
                "displayNick": "tb2494039180",
                "encryptAccountId": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF",
                "nick": "tb2494039180",
                "searchKey": "tb2494039180",
                "searchType": "byNick"
            },
            {
                "accountId": "*************",
                "accountType": 8,
                "bizType": "-1",
                "displayNick": "tb2494039180",
                "encryptAccountId": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF",
                "nick": "tb2494039180",
                "searchKey": "tb2494039180",
                "searchType": "byNick"
            }
        ]
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 买家id

```JS
照满："encryptAccountId": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF"

```

## 查询客户信息

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.user.query',
  param: {
    "encryptId": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF"
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.user.query",
    "data": {
        "buyerCreditIcon": "https://gw.alicdn.com/imgextra/i2/O1CN01Bwd8rx1iojLKQCrp1_!!*************-2-tps-60-56.png",
        "buyerCreditIconCount": 3,
        "buyerCreditLevel": 8,
        "buyerCreditPic": "https://img.alicdn.com/imgextra/i3/O1CN01QZTjHW1F4oegDvuEX_!!*************-2-tps-92-45.png",
        "buyerCreditScore": 261,
        "corporateMember": false,
        "corporateSeller": false,
        "hasActiveShop": false,
        "hasMembership": false,
        "isNewCustomer": false,
        "isShopFans": true,
        "miaoXianSheng": false,
        "openShop": false,
        "sellerCreditIconCount": 0,
        "sellerCreditLevel": 0,
        "sellerCreditScore": 0,
        "sendGoodRate": "100.00%",
        "shopCrowTagList": [],
        "vipInfo": "c",
        "vipLevel": 0
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 邀请下单

```js
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.airisland.invite.order.send',
  param: {
    encryptId: 'RAzN8BQaCakmNLV4DBQGxZrh2qsaF',
    bizDomain: 'taobao',
    encrypType: 'internal',
    buyerNick: 'tb783904683',
    itemProps: '[{"itemId":************,"skuId":5191249981449,"quantity":1,"context":{}}]',
  },
  httpMethod: 'post',
  version: '1.0',
});

{
    "api": "mtop.taobao.qianniu.airisland.invite.order.send",
    "data": {
        "code": "0",
        "data": true
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 查询历史订单

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.trade.history.query',
  param: {
    "securityBuyerUid": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF",
    "pageNum": 1,
    "pageSize": 10
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.trade.history.query",
    "data": {
        "orders": [],
        "pageNum": 0,
        "useNewChangeRefund": true,
        "useNewInsteadRefund": true
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 查询三个月订单

1. orderStatus 都有哪些参数

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.trade.query',
  param: {
    "orderStatus": "",
    "securityBuyerUid": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF"
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.trade.query",
    "data": {
        "orders": [
            {
                "adjustFee": "0.00",
                "afterSaleText": "无售后",
                "bizOrderId": "*******************",
                "buyAmount": 1,
                "cardTypeText": "待发货",
                "category": "待发货",
                "collapse": false,
                "createTime": "2025-07-22 21:26:33",
                "extInfoItems": [],
                "itemList": [
                    {
                        "adjustFee": "0.00",
                        "auctionId": "************",
                        "auctionPrice": "0.10",
                        "auctionTitle": "AI 智能写作助手｜高效产出各类文案",
                        "auctionUrl": "https://item.taobao.com/item.htm?id=************",
                        "bizOrderId": "*******************",
                        "buyAmount": 1,
                        "buyerAmount": 1,
                        "buyerRateStatus": 5,
                        "cardType": "UNDELIVERED_TRADE_ENUM",
                        "cardTypeText": "待发货",
                        "createTime": "2025-07-22 21:26:33",
                        "extInfoItems": [
                            {
                                "code": "EST_CON_TIME",
                                "desc": "预估发货时间",
                                "textBefore": "发货时间",
                                "value": "07月24日21:26前发货"
                            }
                        ],
                        "itemCode": "暂无编号",
                        "logisticsStatus": 1,
                        "oldPrice": "0.10",
                        "operators": [
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"logisticsAss\"}",
                                "behavior": "NORMAL",
                                "code": "logisticsAssistant",
                                "extendInfo": "",
                                "position": 11,
                                "title": "物流助手"
                            },
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"initiateExchange\"}",
                                "behavior": "NORMAL",
                                "code": "refundOrChange",
                                "position": 11,
                                "title": "发起退换"
                            },
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"refundHelp\"}",
                                "behavior": "NORMAL",
                                "code": "refundOrChangeNew",
                                "position": 11,
                                "title": "帮他退款"
                            },
                            {
                                "action": "{\"type\":\"url\",\"param\":\"{\\\"subOrderId\\\":\\\"*******************\\\",\\\"linkSource\\\":\\\"qnPcClient\\\",\\\"dotType\\\":\\\"QUERY_GRAY_DETAIL\\\"}\",\"target\":\"https://ss.taobao.com/malicious/redirect/warning.json\"}",
                                "behavior": "NORMAL",
                                "code": "ksrRisk_detail",
                                "extendInfo": "",
                                "position": 12,
                                "title": "查看详情"
                            }
                        ],
                        "outerId": "暂无编号",
                        "payStatus": 2,
                        "payTime": "2025-07-22 21:26:33",
                        "picUrl": "//img.alicdn.com/bao/uploaded/i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                        "price": "0.10",
                        "refundFee": "0.00",
                        "refundStatus": 9,
                        "skuCode": "暂无编号",
                        "snapshotUrl": "https://trade.taobao.com/trade/detail/tradeSnap.htm?snapShot=true&tradeID=*******************",
                        "subOrderId": "*******************",
                        "supportPriceProtect": false,
                        "tags": [],
                        "tradeAlertActions": [],
                        "underInquiry": false
                    }
                ],
                "moreOperators": [],
                "operators": [
                    {
                        "action": "{\"param\":\"{\\\"biz_order_id\\\":\\\"*******************\\\"}\",\"type\":\"url\",\"target\":\"https://trade.taobao.com/trade/detail/trade_order_detail.htm\"}\n",
                        "behavior": "NORMAL",
                        "code": "orderDetail",
                        "extendInfo": "",
                        "position": 1,
                        "title": "查看详情"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"inviteInvoice\"}",
                        "behavior": "NORMAL",
                        "code": "invoice",
                        "extendInfo": "",
                        "position": 1,
                        "title": "发票"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"ticketCreate\"}",
                        "behavior": "NORMAL",
                        "code": "qianniuWorkOrder",
                        "position": 1,
                        "title": "创建此订单工单"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"orderRemark\"}",
                        "behavior": "NORMAL",
                        "code": "changeMemo",
                        "extendInfo": "",
                        "position": 1,
                        "title": "备注"
                    },
                    {
                        "action": "{\"param\":\"{\\\"bizOrderId\\\":\\\"*******************\\\",\\\"encryptId\\\":\\\"RAzN8BQaCakmNLV4DBQGxZrh2qsaF\\\"}\",\"type\":\"mtop\",\"target\":\"mtop.taobao.bcpush.order.check.card.send?v=2.0\"}",
                        "behavior": "NORMAL",
                        "code": "checkOrder",
                        "position": 2,
                        "title": "核对订单"
                    },
                    {
                        "action": "{\"param\":\"{\\\"tid\\\":\\\"*******************\\\"}\",\"type\":\"popLayer\",\"target\":\"ChangeAddress\"}",
                        "behavior": "NORMAL",
                        "code": "changeAddress",
                        "extendInfo": "",
                        "position": 2,
                        "title": "改址"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"sendCago\"}",
                        "behavior": "NORMAL",
                        "code": "quickDelivery",
                        "extendInfo": "",
                        "position": 2,
                        "title": "发货"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"negotiateCago\"}",
                        "behavior": "NORMAL",
                        "code": "negotiateDelivery",
                        "position": 2,
                        "title": "协商发货"
                    },
                    {
                        "action": "{\"param\":\"{\\\"history\\\":\\\"false\\\",\\\"tid\\\":\\\"*******************\\\",\\\"component\\\":\\\"collection\\\"}\",\"type\":\"native\",\"target\":\"qnComponent\"}",
                        "behavior": "NORMAL",
                        "code": "receipt",
                        "extendInfo": "",
                        "position": 2,
                        "title": "小额收款"
                    },
                    {
                        "action": "{\"param\":\"{\\\"history\\\":\\\"false\\\",\\\"orderId\\\":\\\"*******************\\\",\\\"component\\\":\\\"payment2\\\"}\",\"type\":\"native\",\"target\":\"qnComponent\"}",
                        "behavior": "NORMAL",
                        "code": "payment",
                        "extendInfo": "",
                        "position": 2,
                        "title": "小额打款"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"customComponents\",\"target\":\"quickCopy\"}",
                        "behavior": "NORMAL",
                        "code": "quickCopy",
                        "extendInfo": "",
                        "position": 2,
                        "tag": "new",
                        "tagExpireTime": "2023-12-21 00:00:00",
                        "title": "快捷复制"
                    }
                ],
                "orderPrice": "0.10",
                "overseasUser": false,
                "payTime": "2025-07-22 21:26:33",
                "postFee": "0.00",
                "promotionDetails": [],
                "promotionTotalFee": "0",
                "receiverAddress": "上海市浦东新区***********",
                "receiverMobilePhone": "***********",
                "receiverName": "宫**",
                "refundFee": "0.00",
                "riskOrder": false,
                "sellerFlag": 0,
                "sticky": true,
                "tags": [],
                "underInquiry": true,
                "xsdOrder": false
            },
            {
                "adjustFee": "0.00",
                "afterSaleText": "无售后",
                "bizOrderId": "*******************",
                "buyAmount": 1,
                "cardTypeText": "待发货",
                "category": "待发货",
                "collapse": false,
                "createTime": "2025-07-22 21:26:25",
                "extInfoItems": [],
                "itemList": [
                    {
                        "adjustFee": "0.00",
                        "auctionId": "************",
                        "auctionPrice": "0.10",
                        "auctionTitle": "AI 智能写作助手｜高效产出各类文案",
                        "auctionUrl": "https://item.taobao.com/item.htm?id=************",
                        "bizOrderId": "*******************",
                        "buyAmount": 1,
                        "buyerAmount": 1,
                        "buyerRateStatus": 5,
                        "cardType": "UNDELIVERED_TRADE_ENUM",
                        "cardTypeText": "待发货",
                        "createTime": "2025-07-22 21:26:25",
                        "extInfoItems": [
                            {
                                "code": "EST_CON_TIME",
                                "desc": "预估发货时间",
                                "textBefore": "发货时间",
                                "value": "07月24日21:26前发货"
                            }
                        ],
                        "itemCode": "暂无编号",
                        "logisticsStatus": 1,
                        "oldPrice": "0.10",
                        "operators": [
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"logisticsAss\"}",
                                "behavior": "NORMAL",
                                "code": "logisticsAssistant",
                                "extendInfo": "",
                                "position": 11,
                                "title": "物流助手"
                            },
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"initiateExchange\"}",
                                "behavior": "NORMAL",
                                "code": "refundOrChange",
                                "position": 11,
                                "title": "发起退换"
                            },
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"refundHelp\"}",
                                "behavior": "NORMAL",
                                "code": "refundOrChangeNew",
                                "position": 11,
                                "title": "帮他退款"
                            },
                            {
                                "action": "{\"type\":\"url\",\"param\":\"{\\\"subOrderId\\\":\\\"*******************\\\",\\\"linkSource\\\":\\\"qnPcClient\\\",\\\"dotType\\\":\\\"QUERY_GRAY_DETAIL\\\"}\",\"target\":\"https://ss.taobao.com/malicious/redirect/warning.json\"}",
                                "behavior": "NORMAL",
                                "code": "ksrRisk_detail",
                                "extendInfo": "",
                                "position": 12,
                                "title": "查看详情"
                            }
                        ],
                        "outerId": "暂无编号",
                        "payStatus": 2,
                        "payTime": "2025-07-22 21:26:25",
                        "picUrl": "//img.alicdn.com/bao/uploaded/i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                        "price": "0.10",
                        "refundFee": "0.00",
                        "refundStatus": 9,
                        "skuCode": "暂无编号",
                        "snapshotUrl": "https://trade.taobao.com/trade/detail/tradeSnap.htm?snapShot=true&tradeID=*******************",
                        "subOrderId": "*******************",
                        "supportPriceProtect": false,
                        "tags": [],
                        "tradeAlertActions": [],
                        "underInquiry": false
                    }
                ],
                "moreOperators": [],
                "operators": [
                    {
                        "action": "{\"param\":\"{\\\"biz_order_id\\\":\\\"*******************\\\"}\",\"type\":\"url\",\"target\":\"https://trade.taobao.com/trade/detail/trade_order_detail.htm\"}\n",
                        "behavior": "NORMAL",
                        "code": "orderDetail",
                        "extendInfo": "",
                        "position": 1,
                        "title": "查看详情"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"inviteInvoice\"}",
                        "behavior": "NORMAL",
                        "code": "invoice",
                        "extendInfo": "",
                        "position": 1,
                        "title": "发票"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"ticketCreate\"}",
                        "behavior": "NORMAL",
                        "code": "qianniuWorkOrder",
                        "position": 1,
                        "title": "创建此订单工单"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"orderRemark\"}",
                        "behavior": "NORMAL",
                        "code": "changeMemo",
                        "extendInfo": "",
                        "position": 1,
                        "title": "备注"
                    },
                    {
                        "action": "{\"param\":\"{\\\"bizOrderId\\\":\\\"*******************\\\",\\\"encryptId\\\":\\\"RAzN8BQaCakmNLV4DBQGxZrh2qsaF\\\"}\",\"type\":\"mtop\",\"target\":\"mtop.taobao.bcpush.order.check.card.send?v=2.0\"}",
                        "behavior": "NORMAL",
                        "code": "checkOrder",
                        "position": 2,
                        "title": "核对订单"
                    },
                    {
                        "action": "{\"param\":\"{\\\"tid\\\":\\\"*******************\\\"}\",\"type\":\"popLayer\",\"target\":\"ChangeAddress\"}",
                        "behavior": "NORMAL",
                        "code": "changeAddress",
                        "extendInfo": "",
                        "position": 2,
                        "title": "改址"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"sendCago\"}",
                        "behavior": "NORMAL",
                        "code": "quickDelivery",
                        "extendInfo": "",
                        "position": 2,
                        "title": "发货"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"negotiateCago\"}",
                        "behavior": "NORMAL",
                        "code": "negotiateDelivery",
                        "position": 2,
                        "title": "协商发货"
                    },
                    {
                        "action": "{\"param\":\"{\\\"history\\\":\\\"false\\\",\\\"tid\\\":\\\"*******************\\\",\\\"component\\\":\\\"collection\\\"}\",\"type\":\"native\",\"target\":\"qnComponent\"}",
                        "behavior": "NORMAL",
                        "code": "receipt",
                        "extendInfo": "",
                        "position": 2,
                        "title": "小额收款"
                    },
                    {
                        "action": "{\"param\":\"{\\\"history\\\":\\\"false\\\",\\\"orderId\\\":\\\"*******************\\\",\\\"component\\\":\\\"payment2\\\"}\",\"type\":\"native\",\"target\":\"qnComponent\"}",
                        "behavior": "NORMAL",
                        "code": "payment",
                        "extendInfo": "",
                        "position": 2,
                        "title": "小额打款"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"customComponents\",\"target\":\"quickCopy\"}",
                        "behavior": "NORMAL",
                        "code": "quickCopy",
                        "extendInfo": "",
                        "position": 2,
                        "tag": "new",
                        "tagExpireTime": "2023-12-21 00:00:00",
                        "title": "快捷复制"
                    }
                ],
                "orderPrice": "0.10",
                "overseasUser": false,
                "payTime": "2025-07-22 21:26:25",
                "postFee": "0.00",
                "promotionDetails": [],
                "promotionTotalFee": "0",
                "receiverAddress": "上海市浦东新区***********",
                "receiverMobilePhone": "***********",
                "receiverName": "宫**",
                "refundFee": "0.00",
                "riskOrder": false,
                "sellerFlag": 0,
                "tags": [],
                "underInquiry": false,
                "xsdOrder": false
            },
            {
                "adjustFee": "0.00",
                "afterSaleText": "无售后",
                "bizOrderId": "*******************",
                "buyAmount": 1,
                "cardTypeText": "待收货",
                "category": "未完成",
                "collapse": false,
                "consignTime": "2025-07-20 14:56:11",
                "createTime": "2025-07-20 14:52:49",
                "extInfoItems": [],
                "itemList": [
                    {
                        "adjustFee": "0.00",
                        "auctionId": "953398286214",
                        "auctionPrice": "0.20",
                        "auctionTitle": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本",
                        "auctionUrl": "https://item.taobao.com/item.htm?id=953398286214",
                        "bizOrderId": "*******************",
                        "buyAmount": 1,
                        "buyerAmount": 1,
                        "buyerRateStatus": 5,
                        "cardType": "DELIVERED_TRADE_ENUM",
                        "cardTypeText": "待收货",
                        "createTime": "2025-07-20 14:52:49",
                        "extInfoItems": [
                            {
                                "code": "EST_CON_TIME",
                                "desc": "预估发货时间",
                                "textBefore": "发货时间",
                                "value": "07月22日14:52前发货"
                            }
                        ],
                        "itemCode": "暂无编号",
                        "logisticsStatus": 2,
                        "oldPrice": "0.20",
                        "operators": [
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"logisticsAss\"}",
                                "behavior": "NORMAL",
                                "code": "logisticsAssistant",
                                "extendInfo": "",
                                "position": 11,
                                "title": "物流助手"
                            },
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"initiateExchange\"}",
                                "behavior": "NORMAL",
                                "code": "refundOrChange",
                                "position": 11,
                                "title": "发起退换"
                            },
                            {
                                "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"refundHelp\"}",
                                "behavior": "NORMAL",
                                "code": "refundOrChangeNew",
                                "position": 11,
                                "title": "帮他退款"
                            },
                            {
                                "action": "{\"type\":\"url\",\"param\":\"{\\\"subOrderId\\\":\\\"*******************\\\",\\\"linkSource\\\":\\\"qnPcClient\\\",\\\"dotType\\\":\\\"QUERY_GRAY_DETAIL\\\"}\",\"target\":\"https://ss.taobao.com/malicious/redirect/warning.json\"}",
                                "behavior": "NORMAL",
                                "code": "ksrRisk_detail",
                                "extendInfo": "",
                                "position": 12,
                                "title": "查看详情"
                            }
                        ],
                        "outerId": "暂无编号",
                        "payStatus": 2,
                        "payTime": "2025-07-20 14:52:49",
                        "picUrl": "//img.alicdn.com/bao/uploaded/i4/**********/O1CN01KXoJMu27PQ1yznqHB_!!**********.png",
                        "price": "0.20",
                        "refundFee": "0.00",
                        "refundStatus": 9,
                        "skuCode": "暂无编号",
                        "snapshotUrl": "https://trade.taobao.com/trade/detail/tradeSnap.htm?snapShot=true&tradeID=*******************",
                        "subOrderId": "*******************",
                        "supportPriceProtect": false,
                        "tags": [],
                        "tradeAlertActions": [],
                        "underInquiry": false
                    }
                ],
                "moreOperators": [],
                "operators": [
                    {
                        "action": "{\"param\":\"{\\\"biz_order_id\\\":\\\"*******************\\\"}\",\"type\":\"url\",\"target\":\"https://trade.taobao.com/trade/detail/trade_order_detail.htm\"}\n",
                        "behavior": "NORMAL",
                        "code": "orderDetail",
                        "extendInfo": "",
                        "position": 1,
                        "title": "查看详情"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"inviteInvoice\"}",
                        "behavior": "NORMAL",
                        "code": "invoice",
                        "extendInfo": "",
                        "position": 1,
                        "title": "发票"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"ticketCreate\"}",
                        "behavior": "NORMAL",
                        "code": "qianniuWorkOrder",
                        "position": 1,
                        "title": "创建此订单工单"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"orderRemark\"}",
                        "behavior": "NORMAL",
                        "code": "changeMemo",
                        "extendInfo": "",
                        "position": 1,
                        "title": "备注"
                    },
                    {
                        "action": "{\"param\":\"{\\\"tid\\\":\\\"*******************\\\"}\",\"type\":\"popLayer\",\"target\":\"ChangeAddress\"}",
                        "behavior": "NORMAL",
                        "code": "changeAddress",
                        "extendInfo": "",
                        "position": 2,
                        "title": "改址"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"prolongCago\"}",
                        "behavior": "NORMAL",
                        "code": "delayTakeDelivery",
                        "extendInfo": "",
                        "position": 2,
                        "title": "延长收货"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"showExpressInfo\"}",
                        "behavior": "NORMAL",
                        "code": "getLogisticsInfo",
                        "extendInfo": "",
                        "position": 2,
                        "title": "查看物流"
                    },
                    {
                        "action": "{\"param\":\"{\\\"history\\\":\\\"false\\\",\\\"tid\\\":\\\"*******************\\\",\\\"component\\\":\\\"collection\\\"}\",\"type\":\"native\",\"target\":\"qnComponent\"}",
                        "behavior": "NORMAL",
                        "code": "receipt",
                        "extendInfo": "",
                        "position": 2,
                        "title": "小额收款"
                    },
                    {
                        "action": "{\"param\":\"{\\\"history\\\":\\\"false\\\",\\\"orderId\\\":\\\"*******************\\\",\\\"component\\\":\\\"payment2\\\"}\",\"type\":\"native\",\"target\":\"qnComponent\"}",
                        "behavior": "NORMAL",
                        "code": "payment",
                        "extendInfo": "",
                        "position": 2,
                        "title": "小额打款"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"customComponents\",\"target\":\"quickCopy\"}",
                        "behavior": "NORMAL",
                        "code": "quickCopy",
                        "extendInfo": "",
                        "position": 2,
                        "tag": "new",
                        "tagExpireTime": "2023-12-21 00:00:00",
                        "title": "快捷复制"
                    },
                    {
                        "action": "{\"param\":\"\",\"type\":\"popLayer\",\"target\":\"reissue\"}",
                        "behavior": "NORMAL",
                        "code": "reissue",
                        "extendInfo": "",
                        "position": 2,
                        "tag": "new",
                        "tagExpireTime": "2024-01-15 00:00:00",
                        "title": "补发"
                    }
                ],
                "orderPrice": "0.20",
                "overseasUser": false,
                "payTime": "2025-07-20 14:52:49",
                "postFee": "0.00",
                "promotionDetails": [],
                "promotionTotalFee": "0",
                "receiverAddress": "湖北省武汉市武昌区***********",
                "receiverMobilePhone": "***********",
                "receiverName": "宫**",
                "refundFee": "0.00",
                "riskOrder": false,
                "sellerFlag": 0,
                "tags": [],
                "underInquiry": false,
                "xsdOrder": false
            }
        ],
        "pageNum": 1,
        "useNewChangeRefund": true,
        "useNewInsteadRefund": true
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 发送商品卡片

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.item.recommend.send',
  param: {
    "encryptId": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF",
    "batchItemIds": "[953398286214]",
    "type": -1
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.item.recommend.send",
    "data": {
        "sendCard": true,
        "sendUrl": "https://item.taobao.com/item.htm?id=953398286214"
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 查询商品记录（咨询/购买/足迹）

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.item.record.query',
  param: {
    "encryptId": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF"
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.item.record.query",
    "data": {
        "footPointItemList": [
            {
                "approveStatus": "onsale",
                "buyTime": "1天前",
                "categoryId": 202062620,
                "day": 1753113600000,
                "haveGift": false,
                "itemId": ************,
                "itemUrl": "https://item.taobao.com/item.htm?id=************",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                "price": "0.10",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 8,
                "soldQuantity": 2,
                "tags": "tags:25282,3137538,2943746,2902850,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "timeStamp": "2025-07-22",
                "title": "AI 智能写作助手｜高效产出各类文案",
                "viewTime": "1天前"
            },
            {
                "approveStatus": "onsale",
                "buyTime": "1天前",
                "categoryId": 202062620,
                "day": 1753113600000,
                "haveGift": false,
                "itemId": 947227733604,
                "itemUrl": "https://item.taobao.com/item.htm?id=947227733604",
                "pic": "https://img.alicdn.com/bao/uploaded/i1/**********/O1CN01MMO9Xx27PQ1gJzaEO_!!**********.png",
                "price": "0.10",
                "props": "20000:30025069481;20435:6076017",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值",
                "quantity": 10,
                "soldQuantity": 0,
                "tags": "tags:25282,3137538,2943746,2902850,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3614146,3436226,3444226,2016386,1904386",
                "timeStamp": "2025-07-22",
                "title": "AI 写作服务 智能创作 商业文案代写产品详情页营销策划 定制服务",
                "viewTime": "1天前"
            },
            {
                "approveStatus": "onsale",
                "buyTime": "1天前",
                "categoryId": 202062620,
                "day": 1753113600000,
                "haveGift": false,
                "itemId": 953398286214,
                "itemUrl": "https://item.taobao.com/item.htm?id=953398286214",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01KXoJMu27PQ1yznqHB_!!**********.png",
                "price": "0.20",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 9,
                "soldQuantity": 1,
                "tags": "tags:25282,3137538,2943746,2902850,3326722,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "timeStamp": "2025-07-22",
                "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本",
                "viewTime": "1天前"
            },
            {
                "approveStatus": "onsale",
                "buyTime": "2天前",
                "categoryId": 202062620,
                "day": 1753027200000,
                "haveGift": false,
                "itemId": 953398286214,
                "itemUrl": "https://item.taobao.com/item.htm?id=953398286214",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01KXoJMu27PQ1yznqHB_!!**********.png",
                "price": "0.20",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 9,
                "soldQuantity": 1,
                "tags": "tags:25282,3137538,2943746,2902850,3326722,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "timeStamp": "2025-07-21",
                "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本",
                "viewTime": "2天前"
            },
            {
                "approveStatus": "onsale",
                "buyTime": "3天前",
                "categoryId": 202062620,
                "day": 1752940800000,
                "haveGift": false,
                "itemId": 953398286214,
                "itemUrl": "https://item.taobao.com/item.htm?id=953398286214",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01KXoJMu27PQ1yznqHB_!!**********.png",
                "price": "0.20",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 9,
                "soldQuantity": 1,
                "tags": "tags:25282,3137538,2943746,2902850,3326722,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "timeStamp": "2025-07-20",
                "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本",
                "viewTime": "3天前"
            }
        ],
        "hasMoreFootPrint": true,
        "recentlyBoughtItemList": [
            {
                "approveStatus": "onsale",
                "buyTime": "今天",
                "categoryId": 202062620,
                "day": 1753190793000,
                "haveGift": false,
                "itemId": ************,
                "itemUrl": "https://item.taobao.com/item.htm?id=************",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                "price": "0.10",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 8,
                "soldQuantity": 2,
                "tags": "tags:25282,3137538,2943746,2902850,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "title": "AI 智能写作助手｜高效产出各类文案"
            },
            {
                "approveStatus": "onsale",
                "buyTime": "2天前",
                "categoryId": 202062620,
                "day": 1752994369000,
                "haveGift": false,
                "itemId": 953398286214,
                "itemUrl": "https://item.taobao.com/item.htm?id=953398286214",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01KXoJMu27PQ1yznqHB_!!**********.png",
                "price": "0.20",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 9,
                "soldQuantity": 1,
                "tags": "tags:25282,3137538,2943746,2902850,3326722,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本"
            }
        ],
        "showRecentlyBought": true,
        "underInquiryItemList": [
            {
                "approveStatus": "onsale",
                "buttons": [
                    {
                        "behavior": "NORMAL",
                        "code": "sendItem",
                        "hidden": false,
                        "name": "发送宝贝",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "inviteBuy",
                        "hidden": false,
                        "name": "邀请下单",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "specialDiscount",
                        "hidden": false,
                        "name": "专属优惠",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "discountCalc",
                        "hidden": false,
                        "name": "优惠计算",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "collect",
                        "hidden": false,
                        "name": "收藏",
                        "type": "official"
                    }
                ],
                "categoryId": 202062620,
                "haveGift": false,
                "itemId": ************,
                "itemUrl": "https://item.taobao.com/item.htm?id=************",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                "price": "0.10",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 8,
                "soldQuantity": 2,
                "tags": "tags:25282,3137538,2943746,2902850,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "title": "AI 智能写作助手｜高效产出各类文案"
            },
            {
                "approveStatus": "onsale",
                "buttons": [
                    {
                        "behavior": "NORMAL",
                        "code": "sendItem",
                        "hidden": false,
                        "name": "发送宝贝",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "inviteBuy",
                        "hidden": false,
                        "name": "邀请下单",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "specialDiscount",
                        "hidden": false,
                        "name": "专属优惠",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "discountCalc",
                        "hidden": false,
                        "name": "优惠计算",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "collect",
                        "hidden": false,
                        "name": "收藏",
                        "type": "official"
                    }
                ],
                "categoryId": 202062620,
                "haveGift": false,
                "itemId": 947227733604,
                "itemUrl": "https://item.taobao.com/item.htm?id=947227733604",
                "pic": "https://img.alicdn.com/bao/uploaded/i1/**********/O1CN01MMO9Xx27PQ1gJzaEO_!!**********.png",
                "price": "0.10",
                "props": "20000:30025069481;20435:6076017",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值",
                "quantity": 10,
                "soldQuantity": 0,
                "tags": "tags:25282,3137538,2943746,2902850,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3614146,3436226,3444226,2016386,1904386",
                "title": "AI 写作服务 智能创作 商业文案代写产品详情页营销策划 定制服务"
            },
            {
                "approveStatus": "onsale",
                "buttons": [
                    {
                        "behavior": "NORMAL",
                        "code": "sendItem",
                        "hidden": false,
                        "name": "发送宝贝",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "inviteBuy",
                        "hidden": false,
                        "name": "邀请下单",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "specialDiscount",
                        "hidden": false,
                        "name": "专属优惠",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "discountCalc",
                        "hidden": false,
                        "name": "优惠计算",
                        "type": "official"
                    },
                    {
                        "behavior": "NORMAL",
                        "code": "collect",
                        "hidden": false,
                        "name": "收藏",
                        "type": "official"
                    }
                ],
                "categoryId": 202062620,
                "haveGift": false,
                "itemId": 953398286214,
                "itemUrl": "https://item.taobao.com/item.htm?id=953398286214",
                "pic": "https://img.alicdn.com/bao/uploaded/i4/**********/O1CN01KXoJMu27PQ1yznqHB_!!**********.png",
                "price": "0.20",
                "props": "20000:30025069481;20435:6076017;31004:96551",
                "propsAlias": "",
                "propsName": "20000:30025069481:品牌:无品牌/无注册商标;20435:6076017:充值方式:自动充值;31004:96551:服务类型:其它服务类型",
                "quantity": 9,
                "soldQuantity": 1,
                "tags": "tags:25282,3137538,2943746,2902850,3326722,3203330,3203458,3203266,3203394,3185538,3215426,3213250,3436226,3444226,1904386",
                "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本"
            }
        ]
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 查询订单物流信息

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.alibaba.fulfillment.printorder.consign.logistics.query',
  param: {
    "bizOrderId": "*******************"
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.alibaba.fulfillment.printorder.consign.logistics.query",
    "data": {
        "logisticsInfoList": [
            {
                "goodsList": [
                    {
                        "allPicUrl": "https://img.alicdn.com/tps//i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                        "goodsName": "AI 智能写作助手｜高效产出各类文案",
                        "goodsPicId": "0",
                        "goodsQuantity": 1,
                        "itemId": "************",
                        "itemValue": 10,
                        "orderGoodsId": "837273443077",
                        "sellProperty": "",
                        "taobaoTradeId": "*******************",
                        "tradeGoodUrl": "https://trade.taobao.com/trade/detail/trade_snap.htm?tradeId=*******************"
                    }
                ],
                "logisticsDetailList": [
                    {
                        "desc": "商品已经下单",
                        "time": "2025-07-22 21:26:33",
                        "title": "已下单"
                    }
                ],
                "reissue": false
            }
        ],
        "orderDetailList": [
            {
                "itemDetailInfoList": [
                    {
                        "actualPayAmount": "0.10",
                        "bizOrderId": "*******************",
                        "buyAmount": 1,
                        "itemId": ************,
                        "itemTitle": "AI 智能写作助手｜高效产出各类文案",
                        "picUrl": "//img.alicdn.com/imgextra/i4/**********/O1CN01phC9Mh27PQ1xk21rz_!!**********.png",
                        "subOrderId": "*******************"
                    }
                ],
                "mainBizOrderId": "*******************",
                "price": 0.1
            }
        ],
        "receiver": {
            "address": "百*路2**号7**室",
            "addressKeyWordFlag": false,
            "area": "浦东新区",
            "city": "上海市",
            "detailAddress": "上海^^^上海市^^^浦东新区^^^张江镇^^^百*路2**号7**室",
            "detailAddressTh": "上海 上海市 浦东新区",
            "encryption": true,
            "mobileSource": false,
            "modifyAddress": false,
            "multiAddressFlag": false,
            "name": "宫**",
            "noSplitCharDetailAddress": "上海上海市浦东新区张江镇百*路2**号7**室",
            "phone": "*******4029",
            "prefixAddress": "上海^^^上海市^^^浦东新区^^^张江镇^^^",
            "prov": "上海",
            "town": "张江镇",
            "valid": true
        }
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 订单解密

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.tid.decrypt',
  param: {
    "bizType": "qianniu",
    "tid": "*******************",
    "queryByTid": true
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.tid.decrypt",
    "data": {
        "result": {
            "addressDetail": "张江镇百叶路212号724室",
            "city": "上海市",
            "country": "",
            "decryptDaysAfterOrderEnd": -1,
            "desensitize": false,
            "district": "浦东新区",
            "fullAddress": " 上海 上海市 浦东新区 张江镇百叶路212号724室",
            "history": false,
            "matched": false,
            "mobile": "15071114029",
            "name": "宫照满",
            "orderStatus": 2,
            "privacyProtection": false,
            "relateReceivers": {},
            "state": "上海",
            "town": "张江镇",
            "type": 0,
            "virtualNo": false
        }
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 挂起会话

```JS
await QN.app.invoke({
  api: 'invokeMTopChannelService',
  query: {
    method: 'mtop.taobao.qianniu.cloudkefu.suspend.set',
    param: {
      "account_id": *************,
      "source": 1,
      "is_suspend": true
    },
    httpMethod: 'post',
    version: '1.0'
  }
});

{
    "api": "mtop.taobao.qianniu.cloudkefu.suspend.set",
    "data": {
        "errorCode": 0,
        "errorMap": {},
        "module": true
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 转接到个人客服
```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.forward',
  param: {
    "buyerId": *************, // 买家id
    "toId": **********, // 客户id
    "reason": "转接",
    "options": JSON.stringify({
      "appCid": "*************.1-**********.1#11001@cntaobao", // appCid 就是 ccode
      "buyerDomain": "cntaobao",
      "loginDomain": "cntaobao"
    })
  },
  httpMethod: 'post',
  version: '3.0'
});

{
    "api": "mtop.taobao.qianniu.cloudkefu.forward",
    "data": {
        "errorCode": 0,
        "errorMap": {},
        "module": true
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "3.0"
}
```

## 获取所有客服分组
```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.dispatchgroups.get',
  param: { login_domain: 'cntaobao' },
  httpMethod: 'post',
  version: '2.0',
});

{
    "api": "mtop.taobao.qianniu.cloudkefu.dispatchgroups.get",
    "data": {
        "result": "{\"errorCode\":0,\"errorMap\":{},\"module\":[{\"accountStatusList\":[{\"accountId\":*************,\"clientSuspendStatus\":-1,\"lastClientSuspendStatusCT\":*************,\"lastPcClientOnlineStatusCT\":*************,\"mainAccountId\":**********,\"mobileClientOnlineStatus\":-1,\"mobileOnline\":false,\"nick\":\"yilicheng13:欣儿\",\"pcClientOnlineStatus\":1,\"pcOnline\":true,\"suspend\":true}],\"accounts\":[{\"accountId\":*************,\"accountNick\":\"欣儿\",\"accountSites\":[],\"accountStatus\":1,\"dispatch\":true,\"dispatchStatus\":2,\"nick\":\"yilicheng13:欣儿\",\"openSecurity\":false,\"owedStatus\":1,\"parentNick\":\"yilicheng13\",\"subName\":\"欣儿\",\"subUserId\":*************,\"userId\":**********,\"weight\":300}],\"groupName\":\"示例分组\",\"id\":*********,\"onlineUserNumbersInGroup\":1,\"status\":1,\"userId\":**********},{\"accountStatusList\":[{\"accountId\":**********,\"clientSuspendStatus\":1,\"lastClientSuspendStatusCT\":*************,\"lastPcClientOnlineStatusCT\":*************,\"mainAccountId\":**********,\"mobileClientOnlineStatus\":-1,\"mobileOnline\":false,\"nick\":\"yilicheng13\",\"pcClientOnlineStatus\":1,\"pcOnline\":true,\"suspend\":false}],\"accounts\":[{\"accountId\":*************,\"accountNick\":\"照满\",\"accountSites\":[],\"accountStatus\":1,\"dispatch\":true,\"dispatchStatus\":2,\"nick\":\"yilicheng13:照满\",\"openSecurity\":false,\"owedStatus\":1,\"parentNick\":\"yilicheng13\",\"subName\":\"照满\",\"subUserId\":*************,\"userId\":**********,\"weight\":300},{\"accountId\":*************,\"accountNick\":\"国庆\",\"accountSites\":[],\"accountStatus\":1,\"dispatch\":true,\"dispatchStatus\":2,\"nick\":\"yilicheng13:国庆\",\"openSecurity\":false,\"owedStatus\":1,\"parentNick\":\"yilicheng13\",\"subName\":\"国庆\",\"subUserId\":*************,\"userId\":**********,\"weight\":500},{\"accountId\":**********,\"accountNick\":\"yilicheng13\",\"accountSites\":[],\"accountStatus\":1,\"dispatch\":true,\"dispatchStatus\":2,\"nick\":\"yilicheng13\",\"openSecurity\":false,\"owedStatus\":1,\"subUserId\":**********,\"userId\":**********,\"weight\":500}],\"groupName\":\"客服分组**************\",\"id\":*********,\"onlineUserNumbersInGroup\":1,\"status\":1,\"userId\":**********},{\"accounts\":[],\"groupName\":\"GetGroupAccountInfo_UnDispatchGroup\",\"userId\":**********}],\"success\":true}"
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "2.0"
}
```

## 转接到客服分组

1. 服务端报错，错误原因可能是请求参数不明

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.forward',
  param: {
    "buyerId": *************,
    "toId": **********,
    "reason": "转接",
    "options": JSON.stringify({
      "groupId": *********,
      "appCid": "*************.1-**********.1#11001@cntaobao",
      "forwardType": 2,
      "charset": "utf-8",
      "exceptUsers": "",
      "buyerDomain": "cntaobao",
      "loginDomain": "cntaobao"
    })
  },
  httpMethod: 'post',
  version: '3.0'
});

{
    "api": "mtop.taobao.qianniu.cloudkefu.forward",
    "data": {
        "errorCode": 0,
        "errorMap": {},
        "module": true
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "3.0"
}
```

## 查询店铺优惠券

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.user.shop.coupon.query',
  param: {},
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.user.shop.coupon.query",
    "data": {
        "privateCouponList": [],
        "publicCouponList": [
            {
                "activityId": "acfb71cd53684077a439a1a2f99a3ce3",
                "amount": "1",
                "couponPromotionType": 0,
                "couponType": 1,
                "description": "满1.01减1元",
                "endTime": "2025-08-06 23:59:59",
                "isPublic": true,
                "items": [
                    953398286214
                ],
                "name": "商品立减券0723",
                "personLimit": -1,
                "reserveCount": 1000,
                "startTime": "2025-07-23 00:00:00",
                "templateCode": 116512362368,
                "threshold": "1.01",
                "totalCount": 1000
            }
        ]
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 发送优惠券

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.user.shop.coupon.send',
  param: {
    "name": "店铺优惠卷",
    "activityId": "acfb71cd53684077a439a1a2f99a3ce3",
    "description": "满10000减1000元",
    "encryptId": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF"
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cs.user.shop.coupon.send",
    "data": {
        "result": true
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 客服账号查询

```JS
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.accountstatus.getbyid',
  param: {
    "pageSize": 100
  },
  httpMethod: 'post',
  version: '1.0'
});

{
    "api": "mtop.taobao.qianniu.cloudkefu.accountstatus.getbyid",
    "data": {
        "errorCode": 0,
        "errorMap": {},
        "module": [
            {
                "accountId": **********,
                "clientSuspendStatus": 1,
                "mainAccountId": **********,
                "mobileClientOnlineStatus": -1,
                "mobileOnline": false,
                "nick": "yilicheng13",
                "pcClientOnlineStatus": 1,
                "pcOnline": true,
                "suspend": false
            },
            {
                "accountId": *************,
                "mainAccountId": **********,
                "mobileClientOnlineStatus": -1,
                "mobileOnline": false,
                "nick": "欣儿",
                "pcClientOnlineStatus": 1,
                "pcOnline": true,
                "suspend": false
            },
            {
                "accountId": *************,
                "mainAccountId": **********,
                "mobileClientOnlineStatus": -1,
                "mobileOnline": false,
                "nick": "照满",
                "pcClientOnlineStatus": -1,
                "pcOnline": false,
                "suspend": false
            },
            {
                "accountId": *************,
                "mainAccountId": **********,
                "mobileClientOnlineStatus": -1,
                "mobileOnline": false,
                "nick": "国庆",
                "pcClientOnlineStatus": -1,
                "pcOnline": false,
                "suspend": false
            }
        ]
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}
```

## 发送消息相关接口

1. 发送文本消息到输入框
```JS
await imsdk.invoke('application.insertText2Inputbox',
  { 
    uid: "cntaobaotb783904683", 
    text: "Hello, World!"
  }
)

{}
```
  
