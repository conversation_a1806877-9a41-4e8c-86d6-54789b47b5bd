import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  mode: process.env.NODE_ENV,
  root: resolve(__dirname, '../src/main'),
  envDir: resolve(__dirname, '../'),
  envPrefix: 'VITE_',

  build: {
    outDir: resolve(__dirname, '../dist/main'),
    emptyOutDir: false, // 不要清空目录，因为主进程已经构建了
    minify: process.env.NODE_ENV === 'production',
    sourcemap: true,
    // 指定为 Node.js 环境构建
    target: 'node18',
    lib: {
      entry: resolve(__dirname, '../src/main/preload.ts'),
      formats: ['cjs'],
      fileName: () => 'preload.js'
    },
    rollupOptions: {
      external: [
        'electron',
        // Node.js 内置模块
        'path',
        'fs',
        'os',
        'url',
        'crypto'
      ],
      output: {
        // 确保输出为 CommonJS 格式
        format: 'cjs'
      }
    }
  }
})
