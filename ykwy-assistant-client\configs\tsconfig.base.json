{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "node", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": "../", "paths": {"@/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"], "@main/*": ["src/main/*"]}}, "exclude": ["node_modules", "dist", "release", "build"]}