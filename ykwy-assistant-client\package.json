{"name": "ykwy-assistant-client", "version": "1.0.0", "description": "易康无忧客服助手桌面端", "main": "dist/main/index.js", "private": true, "scripts": {"dev": "concurrently \"pnpm dev:renderer\" \"pnpm dev:main\"", "dev:main": "pnpm build:main && pnpm build:preload && npx electron dist/main/index.js --inspect=5858", "dev:renderer": "pnpm vite --config configs/vite.renderer.ts", "build": "node scripts/build.mjs", "build:main": "pnpm vite build --config configs/vite.main.ts", "build:preload": "pnpm vite build --config configs/vite.preload.ts", "build:renderer": "pnpm vite build --config configs/vite.renderer.ts", "build:electron": "pnpm build && pnpm electron-builder --config scripts/electron-builder.json", "build:win": "pnpm build && pnpm electron-builder --config scripts/electron-builder.json --win", "build:mac": "pnpm build && pnpm electron-builder --config scripts/electron-builder.json --mac", "build:linux": "pnpm build && pnpm electron-builder --config scripts/electron-builder.json --linux", "start": "npx electron dist/main/index.js", "lint": "pnpm eslint src --ext ts,tsx --fix", "typecheck": "pnpm tsc --build", "typecheck:main": "pnpm tsc --project configs/tsconfig.main.json --noEmit", "typecheck:renderer": "pnpm tsc --project configs/tsconfig.renderer.json --noEmit", "clean": "rimraf dist release build", "prepare": "husky", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.62.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^22.10.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^9.1.0", "electron": "37.2.2", "electron-builder": "^26.0.12", "eslint": "^9.0.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "npm-run-all": "^4.1.5", "postcss": "^8.5.0", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "typescript-eslint": "^8.35.1", "vite": "^6.0.0", "vite-plugin-electron": "^0.28.8"}, "build": {"appId": "com.ykwy.assistant-client", "productName": "易康无忧客服助手", "directories": {"output": "release"}, "files": ["dist/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix"]}}