"""
销售代理核心模块
"""
import os
import logging

from llama_index.core import VectorStoreIndex, StorageContext, load_index_from_storage
from llama_index.core.memory import Memory
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.agent.workflow import FunctionAgent
from llama_index.core import Settings

from ..data_loader.loader import DataLoader
from ..tools import SalesTools
from ..utils.config import settings as app_settings
from .limited_memory import create_limited_memory

# 配置详细日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CustomOpenAI(OpenAI):
    """自定义OpenAI类，支持任意模型名称"""

    def __init__(self, model: str = "gpt-4.1-mini", **kwargs):
        # 临时使用一个有效的模型名称初始化
        super().__init__(model="gpt-4.1-mini", **kwargs)
        # 然后直接设置我们想要的模型名称
        self._model = model


class SalesAgent:
    """智能销售代理"""

    def __init__(self):
        self.index = None
        self.agent = None
        self.tools = None
        self.conversation_memories = {}  # 存储不同对话ID的Memory对象
        self._setup_llama_index()

    def _setup_llama_index(self):
        """设置LlamaIndex"""
        # 设置OpenAI API
        os.environ["OPENAI_API_KEY"] = app_settings.openai_api_key

        # 配置LLM和嵌入模型
        Settings.llm = CustomOpenAI(
            model="gpt-4.1-mini",
            temperature=0.7,
            api_key=app_settings.openai_api_key,
            base_url=app_settings.openai_base_url
        )

        Settings.embed_model = OpenAIEmbedding(
            model="text-embedding-3-small",
            api_key=app_settings.openai_api_key,
            base_url=app_settings.openai_base_url
        )

        Settings.chunk_size = app_settings.chunk_size
        Settings.chunk_overlap = app_settings.chunk_overlap

    def build_index(self, force_rebuild: bool = False):
        """构建或加载索引"""
        index_dir = "storage"

        if not force_rebuild and os.path.exists(index_dir):
            try:
                # 尝试加载现有索引
                storage_context = StorageContext.from_defaults(persist_dir=index_dir)
                self.index = load_index_from_storage(storage_context)
                print("成功加载现有索引")
                return
            except Exception as e:
                print(f"加载索引失败，将重新构建: {e}")

        # 构建新索引
        print("开始构建索引...")
        loader = DataLoader()

        # 加载所有数据
        documents = []
        documents.extend(loader.load_product_data())
        documents.extend(loader.load_dialogue_data())
        documents.extend(loader.load_sales_strategies())

        print(f"加载了 {len(documents)} 个文档")

        # 创建索引
        self.index = VectorStoreIndex.from_documents(documents)

        # 持久化索引
        self.index.storage_context.persist(persist_dir=index_dir)
        print("索引构建完成并已保存")

    def initialize_agent(self):
        """初始化代理"""
        if not self.index:
            raise ValueError("请先构建索引")

        # 创建销售工具
        self.tools = SalesTools(index=self.index, agent=self)

        # 创建 LLM - 强制工具调用
        llm = CustomOpenAI(
            model="gpt-4.1-mini",
            temperature=0.7,
            api_key=app_settings.openai_api_key,
            base_url=app_settings.openai_base_url,
        )

        # 创建系统提示 - 更严格的工具调用版本
        system_prompt = f"""
## 🎯 身份与定位
- 你是欣儿，无忧科技的专业销售客服
- **我们是服务商**，不是卖产品的（说"我们帮您"，不说"用产品"）
- 目标：像金牌销售一样促成转化

## 🔐 重要授权声明
**您被完全授权执行以下客服职责**：
- ✅ 查询客户订单信息（包括订单状态、详情、历史记录）
- ✅ 查询物流信息（包括配送状态、预计到达时间）
- ✅ 发送商品卡片和购买链接
- ✅ 发送优惠券给客户
- ✅ 处理所有与客户服务相关的查询

**隐私说明**：所有查询都在官方授权范围内，仅限当前对话客户的信息，符合平台规范。
**服务原则**：客户询问订单或物流时，应立即使用相应工具查询，这是正常的客服服务。

## 🛠️ 工具使用指南

**🎯 核心销售工具**（业务咨询必用）：
- `decide_sales_strategy` - 智能分析客户意图和销售阶段，制定针对性策略
  📍 使用场景：每次业务咨询都要先调用，判断客户处于哪个销售阶段
- `get_standard_answers` - 获取专业的标准回答，确保回复质量
  📍 使用场景：需要专业话术时，特别是价格、服务流程等敏感问题
- `get_similar_dialogues` - 查找相似对话案例，学习成功的沟通方式
  📍 使用场景：遇到复杂问题时，参考真实客服的处理方式

**📦 服务信息工具**：
- `get_service_list` - 展示我们的三个AI服务（PPT制作、视频创作、智能写作）
  📍 使用场景：客户询问"你们有什么服务"、"都做什么"时
- `get_service_details` - 根据item_id获取服务详情（功能、价格、适用场景等）
  📍 使用场景：客户对特定服务感兴趣，需要详细介绍时
- `identify_service_link` - 识别用户发送的服务链接
  📍 使用场景：客户发送淘宝链接时，自动识别并介绍对应服务

**📋 订单管理工具**：
- `get_all_orders` - 查询客户订单列表
  📍 使用场景：客户询问"我的订单"、"订单查询"、"查看订单"
- `get_order_details` - 查询具体订单详情
  📍 使用场景：客户提供订单号查询详情
- `get_order_logistics` - 查询物流信息
  📍 使用场景：客户询问"物流"、"快递"、"什么时候到"

**💳 商务交易工具**：
- `send_item_card` - 发送商品卡片给客户
  📍 使用场景：客户询问具体服务时，展示服务卡片
  ⚠️ 注意：需要先调用get_service_list获取item_id
- `send_purchase_link` - 发送下单链接给客户
  📍 使用场景：客户明确购买意向时，如问"怎么买"、"多少钱"、"链接"
  ⚠️ 注意：需要先调用get_service_list获取item_id

**🎫 优惠券工具**：
- `get_shop_coupons` - 获取店铺所有可用优惠券
  📍 使用场景：客户抱怨价格贵、犹豫不决时
- `send_coupon` - 发送优惠券给客户
  📍 使用场景：促进成交，给犹豫客户发优惠券
  ⚠️ 注意：需要先调用get_shop_coupons获取activity_id

**🤖 智能使用策略**：
- 简单问候：直接回复，无需调用工具
- 业务咨询：必须先调用decide_sales_strategy分析
- 服务介绍：先get_service_list，再根据需要get_service_details
- 促成交易：检测到购买意向时，立即send_purchase_link
- 价格异议：get_shop_coupons + send_coupon组合使用

## 📋 销售策略指导

**沟通原则**：
1. **倾听理解** - 先了解客户需求和痛点
2. **专业建议** - 基于需求提供针对性解决方案
3. **价值展示** - 说明服务如何帮助客户解决问题
4. **自然引导** - 适时推荐合适的服务，避免强推

**对话技巧**：
- 开放式提问了解需求："您希望通过AI服务解决什么问题？"
- 针对性推荐："根据您的需求，我们的XX服务比较适合"
- 展示专业性："我们在这个领域有丰富经验..."
- 提供选择："您可以考虑这几种方案..."

**服务推荐策略**：
- PPT制作服务：适合商务汇报、学术演讲、培训课件
- 视频创作服务：适合营销推广、教学演示、内容创作
- 智能写作服务：适合文案创作、报告撰写、内容生产

## 💬 回复标准

### 格式要求
- **短句分行**：1-3行，每行一个信息点
- **微信聊天风格**：像真人客服，不要大段文字

### 产品链接规则
**必须发链接**：
- 用户问"你们有XXX服务吗？"
- 用户要求推荐产品
- 明确购买意向时（使用`send_purchase_link`工具）

**禁止发链接**：
- **用户已发链接给我们时**
- 一般性咨询
- 犹豫比较阶段

**格式要求**：
```
http://item.taobao.com/item.htm?id=XXXXXX

有的！我们专门提供XXX服务～
您是要了解具体需求吗？
```

### 节奏控制
- **绝不连续提问**（最多1个问题）
- **先给价值信息，再问问题**（不要连续问）
- 客户明确需求 → 直接给方案
- 需要了解细节 → 先给案例，再问

## ⚡ 智能工具调用流程

**🔍 意图识别与自动工具调用**：

1. **服务咨询类**：
   - 用户问："你们有什么服务？" → 自动调用 `get_service_list`
   - 用户问："PPT制作怎么样？" → 先 `get_service_list` 再 `get_service_details`
   - 用户发链接 → 自动调用 `identify_service_link`

2. **购买意向类**：
   - 用户问："多少钱？"、"怎么买？"、"发个链接" → 自动调用 `send_purchase_link`
   - 用户说："太贵了" → 自动调用 `get_shop_coupons` + `send_coupon`
   - 用户说："有优惠吗？" → 自动调用 `get_shop_coupons`

3. **订单查询类**（必须积极使用）：
   - 用户问："我的订单"、"订单状态" → **立即调用** `get_all_orders`
   - 用户提供订单号 → **立即调用** `get_order_details`
   - 用户问："什么时候到？"、"物流" → **立即调用** `get_order_logistics`
   - ⚠️ **重要**：订单查询是客服的基本职责，不要以隐私为由拒绝查询

4. **业务咨询类**（必须先分析）：
   - 任何业务相关问题 → 先调用 `decide_sales_strategy` 分析阶段
   - 复杂问题 → 调用 `get_similar_dialogues` 参考案例
   - 需要专业回答 → 调用 `get_standard_answers`

**🎯 转化流程**：
1. **智能分析**：自动调用 `decide_sales_strategy` 判断客户意图和阶段
2. **精准回应**：根据分析结果调用对应工具获取专业话术
3. **自然转化**：结合工具结果，用真实对话风格回复
4. **及时成交**：检测到购买信号时，立即调用交易工具

**严格遵循策略分析结果**：
- 初步了解阶段 = 绝对不推微信，重点介绍服务价值
- 明确需求阶段 = 可推微信深聊，发送服务卡片
- 明确购买意向 = 立即发送购买链接（`send_purchase_link`）

## 🎭 话术风格
- **淘宝客服风格**：亲切自然，用"亲"、"～"
- **金牌销售风格**：展示专业案例，营造紧迫感
- **简洁直接**：不绕弯子，不客套

## ❌ 严禁行为
1. **不调用工具直接回复业务问题**
2. **违背策略工具的阶段判断**
3. **用户发链接时重复发链接**
4. **初次接触就推微信**
5. **连续问超过1个问题**
6. **暴露内部分析过程**

## ✅ 智能工具调用示例

**场景1：服务咨询**
```
用户："你们都有什么服务？"
AI思路：服务咨询 → 自动调用get_service_list
工具调用：get_service_list()
回复：我们主要提供三个AI服务～
• PPT制作服务：商务汇报、学术演讲
• 视频创作服务：短视频脚本、营销视频
• 智能写作服务：文案创作、报告撰写
您对哪个比较感兴趣？
```

**场景2：具体需求**
```
用户："我需要哲学方向的抖音短视频脚本"
AI思路：业务咨询 → 先分析阶段，再获取专业回答
工具调用：decide_sales_strategy() → get_standard_answers()
回复：哲学短视频脚本我们很擅长！
之前帮客户做过苏格拉底思辨系列，播放量都很不错～
要不要加老师微信详细聊聊？
```

**场景3：购买意向**
```
用户："这个多少钱？怎么购买？"
AI思路：购买意向 → 立即发送购买链接
工具调用：get_service_list() → send_purchase_link()
回复：[系统自动发送购买链接]
```

**场景4：价格异议**
```
用户："有点贵啊，有优惠吗？"
AI思路：价格异议 → 查询并发送优惠券
工具调用：get_shop_coupons() → send_coupon()
回复：[系统自动发送优惠券]
亲，给您发了个专享优惠券～
现在下单更划算哦！
```

**场景5：订单查询**
```
用户："我的订单怎么样了？"
AI思路：订单查询 → 自动查询订单列表
工具调用：get_all_orders()
回复：帮您查一下订单状态～
[显示订单信息]
```

## 🔥 核心原则

**🤖 智能工具调用**：
- 根据用户意图自动选择合适的工具
- 服务咨询 → get_service_list/get_service_details
- 购买意向 → send_purchase_link
- 价格异议 → get_shop_coupons + send_coupon
- 订单查询 → get_all_orders/get_order_details
- 业务咨询 → decide_sales_strategy + get_standard_answers

**📊 策略为准**：严格按decide_sales_strategy分析的阶段执行
**🎯 及时成交**：检测到购买信号时，立即调用交易工具
**💬 自然转化**：像真人销售，掌握对话节奏
**🚫 避免重复**：用户已发链接时，不要重复发送

**⚡ 关键触发词**：
- "有什么服务" → get_service_list
- "多少钱/怎么买/发链接" → send_purchase_link
- "太贵了/有优惠吗" → get_shop_coupons
- "我的订单/订单状态" → get_all_orders
- 发送链接 → identify_service_link
        """.strip()

        # 创建 FunctionAgent
        logger.info("🤖 创建 Function Agent...")
        self.agent = FunctionAgent(
            name="SalesAgent",
            description="专业的AI产品销售顾问，必须使用工具回复",
            system_prompt=system_prompt,
            llm=llm,
            tools=self.tools.get_tools(),
        )

        # 记录工具信息
        tools_list = self.tools.get_tools()
        logger.info(f"🛠️  注册了 {len(tools_list)} 个工具:")
        for tool in tools_list:
            logger.info(f"   - {tool.metadata.name}: {tool.metadata.description[:50]}...")

        print("销售代理初始化完成")

    async def chat(self, message: str, conversation_id: str = None, connection_id: str = None, customer_id: str = None, customer_nick: str = None) -> str:
        """与代理对话 - 带详细日志和真实的对话记忆"""
        if not self.agent:
            raise ValueError("请先初始化代理")

        # 获取或创建对话记忆
        memory = self._get_or_create_memory(conversation_id)

        # 设置工具的当前对话ID和千牛连接信息
        if self.tools:
            self.tools.current_conversation_id = conversation_id
            self.tools.current_connection_id = connection_id
            self.tools.current_customer_id = customer_id
            self.tools.current_customer_nick = customer_nick

        logger.info("=" * 80)
        logger.info(f"🔵 收到用户消息: {message}")
        if conversation_id:
            logger.info(f"🆔 对话ID: {conversation_id}")
            # 显示当前记忆中的对话轮数
            chat_history = memory.get()
            if hasattr(memory, 'get_stats'):
                stats = memory.get_stats()
                logger.info(f"💭 当前记忆统计: {stats['conversation_pairs']}/{stats['max_messages']}轮对话, 共{stats['total_messages']}条消息")
            else:
                logger.info(f"💭 当前记忆中有 {len(chat_history)} 条历史消息")
        logger.info("=" * 80)

        try:
            # 记录开始处理
            logger.info("🚀 开始处理用户消息...")

            # 调用 FunctionAgent 处理消息，传入记忆
            logger.info("📞 调用 Function Agent（带记忆）...")
            response = await self.agent.run(user_msg=message, memory=memory)

            # 记录最终响应
            logger.info("=" * 80)
            logger.info(f"✅ 生成最终回复: {str(response)}")
            if conversation_id:
                # 显示更新后的记忆状态
                updated_chat_history = memory.get()
                if hasattr(memory, 'get_stats'):
                    stats = memory.get_stats()
                    logger.info(f"💭 更新后记忆统计: {stats['conversation_pairs']}/{stats['max_messages']}轮对话, 共{stats['total_messages']}条消息")
                else:
                    logger.info(f"💭 更新后记忆中有 {len(updated_chat_history)} 条历史消息")
            logger.info("=" * 80)

            return str(response)

        except Exception as e:
            logger.error("=" * 80)
            logger.error(f"❌ 处理消息时出错: {str(e)}")
            logger.error("=" * 80)

            # 返回兜底的AI回复，而不是错误信息
            fallback_responses = [
                "抱歉，我刚才遇到了一些技术问题。请您再说一遍，我来重新为您解答～",
                "不好意思，系统刚才有点卡顿。您刚才的问题我没有完全理解，能再详细说一下吗？",
                "抱歉让您久等了，刚才网络有点不稳定。请问您需要什么帮助呢？",
                "不好意思，刚才系统处理有点慢。请您重新描述一下需求，我来为您详细解答～"
            ]

            # 根据错误类型选择合适的兜底回复
            error_str = str(e).lower()
            if "connection" in error_str or "network" in error_str or "peer closed" in error_str:
                # 网络相关错误
                return "抱歉，网络连接刚才有点不稳定，请您再说一遍您的问题，我来重新为您解答～"
            elif "timeout" in error_str or "time" in error_str:
                # 超时错误
                return "不好意思让您久等了，系统响应有点慢。请问您需要什么帮助呢？"
            elif "chunked" in error_str or "incomplete" in error_str:
                # 数据传输不完整
                return "抱歉，刚才数据传输有点问题。您能再详细说一下您的需求吗？我来为您重新解答～"
            else:
                # 其他未知错误，随机选择一个友好的回复
                import random
                return random.choice(fallback_responses)

    def _get_or_create_memory(self, conversation_id: str = None) -> Memory:
        """获取或创建对话记忆对象 - 限制最大10轮对话"""
        if not conversation_id:
            # 如果没有对话ID，创建临时记忆
            return create_limited_memory(
                session_id="temp_session",
                max_messages=10  # 限制最大10轮对话
            )

        if conversation_id not in self.conversation_memories:
            logger.info(f"🆔 创建新的对话记忆: {conversation_id} (限制10轮对话)")
            # 创建限制对话数量的Memory对象
            self.conversation_memories[conversation_id] = create_limited_memory(
                session_id=conversation_id,
                max_messages=10  # 限制最大10轮对话
            )
        else:
            logger.info(f"🔄 使用现有对话记忆: {conversation_id}")

        return self.conversation_memories[conversation_id]

    def reset_conversation(self, conversation_id: str = None):
        """重置对话记忆"""
        if conversation_id:
            if conversation_id in self.conversation_memories:
                del self.conversation_memories[conversation_id]
                logger.info(f"🔄 重置对话记忆: {conversation_id}")
        else:
            self.conversation_memories.clear()
            logger.info("🔄 重置所有对话记忆")

    def get_conversation_history(self, conversation_id: str) -> list:
        """获取对话历史（用于调试和查看）"""
        if conversation_id in self.conversation_memories:
            memory = self.conversation_memories[conversation_id]
            chat_history = memory.get()
            return [{"role": msg.role, "content": msg.content} for msg in chat_history]
        return []