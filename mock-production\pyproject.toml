[tool.poetry]
name = "taobao-product-information-agent"
version = "0.1.0"
description = "基于LlamaIndex的淘宝产品信息智能销售代理"
authors = ["AI Assistant <<EMAIL>>"]
readme = "README.md"
packages = [{include = "sales_agent", from = "src"}]

[tool.poetry.dependencies]
python = "^3.9.2"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
openai = "^1.3.0"
pydantic = "^2.5.0"
python-multipart = "^0.0.6"
aiofiles = "^23.2.0"
llama-index = "^0.12.50"
llama-index-llms-openai = "^0.4.0"
llama-index-agent-openai = "^0.4.0"
llama-index-embeddings-openai = "^0.3.1"
python-dotenv = "^1.0.0"
pandas = "^2.1.0"
pydantic-settings = "^2.10.1"
aiohttp = "^3.9.0"
requests = "^2.31.0"
httpx = "^0.27.0"
chromadb = "^1.0.15"
openpyxl = "^3.1.5"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"


[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88
