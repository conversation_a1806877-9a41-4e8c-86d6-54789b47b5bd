# 易康无忧客服助手桌面端

<p align="center">
  <img src="https://img.shields.io/badge/Electron-37.2.2-47848F?style=for-the-badge&logo=electron" alt="Electron Version">
  <img src="https://img.shields.io/badge/React-19.0.0-61DAFB?style=for-the-badge&logo=react" alt="React Version">
  <img src="https://img.shields.io/badge/TypeScript-5.7.3-3178C6?style=for-the-badge&logo=typescript" alt="TypeScript Version">
  <img src="https://img.shields.io/badge/Vite-6.0.0-646CFF?style=for-the-badge&logo=vite" alt="Vite Version">
</p>

<p align="center">
  基于 Electron + React + TypeScript 开发的智能客服助手桌面应用，专为提升客服工作效率而设计。
</p>

## 📋 目录

- [功能特性](#-功能特性)
- [技术栈](#-技术栈)
- [项目结构](#-项目结构)
- [环境要求](#-环境要求)
- [快速开始](#-快速开始)
- [配置说明](#-配置说明)
- [使用指南](#-使用指南)
- [开发指南](#-开发指南)
- [构建部署](#-构建部署)
- [常见问题](#-常见问题)
- [更新日志](#-更新日志)
- [贡献指南](#-贡献指南)
- [许可证](#-许可证)

## ✨ 功能特性

### 🤖 AI 智能推荐

- **实时分析对话内容**，自动生成个性化回复建议
- **智能推荐算法**，基于历史对话和客户画像优化建议质量
- **多场景适配**，支持售前咨询、售后服务、投诉处理等多种场景
- **置信度评分**，为每条建议提供可信度参考

### 🎯 一键高效操作

- **一键发送消息**，点击即可通过后端 API 转发到千牛客户端
- **快速回复模板**，预设常用回复内容，提升响应速度
- **批量操作支持**，同时处理多个客户咨询
- **消息队列管理**，确保消息发送的可靠性和时序性

### 📱 智能悬浮窗口

- **右侧智能悬浮**，不遮挡工作区域，与其他应用完美协作
- **自适应定位**，根据屏幕分辨率和工作习惯智能调整位置
- **透明度控制**，支持窗口透明度调节，减少视觉干扰
- **窗口状态记忆**，自动保存和恢复窗口位置、大小等设置

### 🔄 实时状态同步

- **千牛会话监听**，实时监控客户端会话切换，无延迟响应
- **WebSocket 长连接**，确保状态同步的实时性和稳定性
- **离线状态处理**，网络中断后自动重连，不丢失任何消息
- **多客户端同步**，支持多个千牛客户端同时连接

### ⚡ 全局快捷操作

- **全局快捷键**，支持 `Ctrl+Shift+A` (Windows/Linux) 或 `Cmd+Shift+A` (macOS) 快速呼出/隐藏
- **自定义快捷键**，根据个人习惯配置常用操作的快捷键
- **快捷键冲突检测**，避免与其他应用快捷键冲突
- **操作提示**，新手友好的快捷键提示系统

### 🎨 现代化用户界面

- **Material Design 3.0** 设计语言，提供一致的视觉体验
- **深色/浅色主题**，支持自动切换和手动选择
- **响应式布局**，适配不同屏幕尺寸和分辨率
- **无障碍设计**，支持键盘导航和屏幕阅读器

### 🔧 高级功能

- **会话录制回放**，记录重要对话用于质量分析
- **数据统计分析**，提供详细的工作效率和客户满意度报告
- **插件扩展系统**，支持第三方功能扩展
- **多语言支持**，界面支持中文、英文等多种语言

## 🛠 技术栈

### 前端技术

- **[Electron](https://electronjs.org/)** `37.2.2` - 跨平台桌面应用框架
- **[React](https://reactjs.org/)** `19.0.0` - 现代化前端 UI 库
- **[TypeScript](https://typescriptlang.org/)** `5.7.3` - 类型安全的 JavaScript 超集
- **[Vite](https://vitejs.dev/)** `6.0.0` - 新一代前端构建工具

### UI 组件库

- **[Radix UI](https://radix-ui.com/)** - 无样式、可访问的 UI 组件库
- **[Tailwind CSS](https://tailwindcss.com/)** `3.4.17` - 实用优先的 CSS 框架
- **[Lucide React](https://lucide.dev/)** - 精美的 SVG 图标库
- **[class-variance-authority](https://cva.style/)** - 类型安全的 CSS 样式变体管理

### 状态管理

- **[TanStack Query](https://tanstack.com/query)** `5.62.0` - 强大的异步状态管理
- **[Zustand](https://zustand-demo.pmnd.rs/)** `5.0.6` - 轻量级状态管理库
- **[React Hook Form](https://react-hook-form.com/)** `7.58.1` - 高性能表单管理

### 开发工具

- **[ESLint](https://eslint.org/)** - 代码质量检查工具
- **[Prettier](https://prettier.io/)** - 代码格式化工具
- **[electron-builder](https://electron.build/)** - Electron 应用打包工具

## 📁 项目结构

```
ykwy-assistant-client/
├── .vscode/                    # VSCode 编辑器配置
│   ├── settings.json          # 工作区设置
│   └── extensions.json        # 推荐扩展
├── configs/                   # 配置文件目录
│   ├── vite.main.ts          # 主进程 Vite 配置
│   ├── vite.renderer.ts      # 渲染进程 Vite 配置
│   ├── tsconfig.base.json    # 基础 TypeScript 配置
│   ├── tsconfig.main.json    # 主进程 TypeScript 配置
│   └── tsconfig.renderer.json # 渲染进程 TypeScript 配置
├── scripts/                   # 构建脚本
│   ├── build.mjs             # 统一构建脚本
│   └── electron-builder.json # 打包配置
├── src/                       # 源代码目录
│   ├── main/                 # Electron 主进程
│   │   ├── index.ts          # 主进程入口文件
│   │   ├── preload.ts        # 预加载脚本
│   │   └── utils.ts          # 主进程工具函数
│   ├── renderer/             # React 渲染进程
│   │   ├── components/       # React 组件
│   │   │   ├── AssistantPanel.tsx  # 主助手面板
│   │   │   └── ui/          # 基础 UI 组件
│   │   ├── hooks/           # 自定义 React Hooks
│   │   │   ├── useAIRecommendations.ts  # AI推荐Hook
│   │   │   ├── useQianNiuMonitor.ts     # 千牛监控Hook
│   │   │   └── useSendMessage.ts        # 消息发送Hook
│   │   ├── lib/             # 工具库
│   │   │   ├── api-client.ts         # API客户端
│   │   │   └── utils.ts              # 通用工具函数
│   │   ├── styles/          # 样式文件
│   │   │   └── globals.css           # 全局样式
│   │   ├── App.tsx          # 主应用组件
│   │   ├── main.tsx         # React 入口文件
│   │   └── index.html       # HTML 模板
│   └── shared/              # 共享代码
│       ├── types/           # 全局类型定义
│       │   └── index.ts     # 主要类型定义
│       └── utils/           # 共享工具函数
│           └── index.ts     # 工具函数入口
├── public/                   # 静态资源
│   └── icon.ico             # 应用图标
├── dist/                    # 构建输出目录
│   ├── main/               # 主进程构建输出
│   └── renderer/           # 渲染进程构建输出
├── release/                 # 应用打包输出
├── .env                    # 环境变量文件
├── .env.example            # 环境变量示例文件
├── package.json            # 项目配置文件
├── tsconfig.json           # 根 TypeScript 配置
├── tailwind.config.js      # Tailwind CSS 配置
├── postcss.config.js       # PostCSS 配置
├── eslint.config.mjs       # ESLint 配置
└── README.md              # 项目说明文档
```

## 📋 环境要求

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最低 4GB RAM，推荐 8GB+
- **存储**: 至少 500MB 可用空间
- **网络**: 稳定的互联网连接

### 开发环境

- **[Node.js](https://nodejs.org/)** >= 18.0.0
- **[pnpm](https://pnpm.io/)** >= 8.0.0 (推荐) 或 npm >= 9.0.0
- **[Git](https://git-scm.com/)** >= 2.0.0

### 运行时依赖

- **后端 API 服务** - 默认运行在 `http://localhost:3002`
- **千牛客户端** - 需要安装并配置千牛连接器

## 🚀 快速开始

### 1. 克隆项目

```bash
# 使用 Git 克隆项目
git clone https://github.com/ykwy/ykwy-assistant-client.git

# 进入项目目录
cd ykwy-assistant-client
```

### 2. 安装依赖

```bash
# 使用 pnpm 安装依赖 (推荐)
pnpm install

# 或使用 npm
npm install
```

### 3. 环境配置

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑环境变量文件
# 根据实际情况修改配置值
notepad .env  # Windows
# 或
nano .env     # macOS/Linux
```

### 4. 启动开发环境

```bash
# 启动完整开发环境（推荐）
pnpm dev

# 或分别启动各个进程
pnpm dev:renderer  # 启动渲染进程开发服务器
pnpm dev:main      # 启动主进程（需要在新终端中运行）
```

### 5. 验证安装

启动成功后，您应该看到：

- 渲染进程开发服务器运行在 `http://localhost:5174`
- Electron 应用窗口自动打开
- 控制台显示 "✅ 应用初始化完成"

## ⚙️ 配置说明

### 环境变量配置

项目使用 `.env` 文件进行配置管理。主要配置项包括：

#### 基础配置

```env
# 应用基本信息
VITE_APP_TITLE=易康无忧客服助手
VITE_APP_VERSION=1.0.0

# 后端服务地址
VITE_API_URL=http://localhost:3002
VITE_WS_URL=ws://localhost:3002
```

#### 开发环境配置

```env
# 开发服务器端口
VITE_DEV_SERVER_PORT=5174

# 窗口配置
VITE_WINDOW_WIDTH=1200
VITE_WINDOW_HEIGHT=800
VITE_WINDOW_ALWAYS_ON_TOP=true
```

#### 认证配置

```env
# 自动登录配置（开发环境）
VITE_AUTO_LOGIN_EMAIL=<EMAIL>
VITE_AUTO_LOGIN_PASSWORD=test123
VITE_ORGANIZATION_ID=your-organization-id
```

#### 功能开关

```env
# AI推荐功能
VITE_AI_RECOMMENDATIONS_ENABLED=true
VITE_AI_RECOMMENDATIONS_COUNT=3

# 调试配置
VITE_LOG_LEVEL=info
VITE_DEBUG_PANEL=false
```

详细配置选项请参考 `.env.example` 文件。

### 千牛客户端配置

1. **安装千牛客户端**

   - 下载并安装指定版本的千牛客户端
   - 确保千牛客户端正常登录并能接收消息

2. **配置连接器**

   - 运行 `ykwy-assistant-connector` 工具
   - 按照提示完成千牛客户端连接配置

3. **验证连接**
   - 在助手应用中查看连接状态
   - 确保能够正常监听会话切换事件

## 📖 使用指南

### 基本操作

#### 启动应用

- **开发模式**: 运行 `pnpm dev`
- **生产模式**: 双击安装后的应用图标

#### 窗口控制

- **显示/隐藏**: `Ctrl+Shift+A` (Windows/Linux) 或 `Cmd+Shift+A` (macOS)
- **置顶切换**: 点击标题栏的图钉图标
- **最小化**: 点击标题栏的最小化按钮
- **关闭**: 点击标题栏的关闭按钮

#### AI 推荐使用

1. **获取推荐**: 应用会自动监听千牛会话切换，获取当前对话的 AI 推荐
2. **发送消息**: 点击推荐内容卡片，消息将自动发送到千牛客户端
3. **刷新推荐**: 点击刷新按钮或使用快捷键 `F5`

### 高级功能

#### 自定义配置

- 通过设置面板调整窗口行为、快捷键、主题等
- 配置 AI 推荐的频率和数量
- 设置消息发送的确认方式

#### 会话管理

- 查看当前活跃的千牛连接状态
- 监控会话切换历史
- 管理离线消息队列

#### 性能优化

- 启用/禁用不需要的功能以提升性能
- 调整轮询频率以平衡实时性和资源消耗
- 配置缓存策略以减少网络请求

## 🛠 开发指南

### 开发环境设置

1. **推荐的编辑器**: [Visual Studio Code](https://code.visualstudio.com/)
2. **必要的扩展**:
   - TypeScript 语言支持
   - ESLint 代码检查
   - Prettier 代码格式化
   - Tailwind CSS 智能提示

### 项目脚本说明

```bash
# 开发相关
pnpm dev                    # 启动完整开发环境
pnpm dev:main              # 仅启动主进程
pnpm dev:renderer          # 仅启动渲染进程

# 构建相关
pnpm build                 # 完整构建
pnpm build:main            # 构建主进程
pnpm build:renderer        # 构建渲染进程

# 打包相关
pnpm build:electron        # 打包 Electron 应用
pnpm build:win             # 仅打包 Windows 版本
pnpm build:mac             # 仅打包 macOS 版本
pnpm build:linux           # 仅打包 Linux 版本

# 代码质量
pnpm lint                  # 运行 ESLint 检查
pnpm typecheck             # TypeScript 类型检查
pnpm typecheck:main        # 主进程类型检查
pnpm typecheck:renderer    # 渲染进程类型检查

# 工具命令
pnpm clean                 # 清理构建文件
pnpm start                 # 运行构建后的应用
```

### 调试指南

#### 主进程调试

1. 启动开发环境：`pnpm dev:main`
2. 主进程会以调试模式启动，监听端口 5858
3. 使用 Chrome DevTools 或 VS Code 连接调试

#### 渲染进程调试

1. 启动渲染进程：`pnpm dev:renderer`
2. 在 Electron 应用中按 `F12` 打开开发者工具
3. 或在浏览器中访问 `http://localhost:5174` 进行调试

#### 日志查看

- **主进程日志**: 查看启动 Electron 的终端输出
- **渲染进程日志**: 查看 Electron 开发者工具的 Console 标签页
- **网络请求**: 查看开发者工具的 Network 标签页

### 代码规范

#### TypeScript 规范

- 使用严格的 TypeScript 配置
- 为所有函数和接口提供类型注解
- 优先使用类型推导而非显式类型声明

#### React 规范

- 使用函数组件和 Hooks
- 遵循组件单一职责原则
- 使用 React.memo 和 useMemo 优化性能

#### 样式规范

- 使用 Tailwind CSS 进行样式开发
- 遵循移动优先的响应式设计原则
- 使用 CSS 变量支持主题切换

## 📦 构建部署

### 本地构建

```bash
# 完整构建流程
pnpm build

# 构建完成后，产物位于 dist/ 目录
# - dist/main/     主进程构建产物
# - dist/renderer/ 渲染进程构建产物
```

### 应用打包

```bash
# 打包当前平台版本
pnpm build:electron

# 打包指定平台版本
pnpm build:win     # Windows (NSIS 安装包)
pnpm build:mac     # macOS (DMG 镜像)
pnpm build:linux   # Linux (AppImage)

# 打包产物位于 release/ 目录
```

### 打包配置

打包配置文件位于 `scripts/electron-builder.json`：

```json
{
  "appId": "com.ykwy.assistant.client",
  "productName": "YKWY Assistant Client",
  "directories": {
    "output": "release"
  },
  "files": ["dist/**/*", "package.json"]
}
```

### CI/CD 部署

项目已配置完整的 GitHub Actions 自动化构建流程，包含以下工作流：

#### 📋 工作流说明

**多平台构建发布工作流** (`.github/workflows/build-release.yml`)
- **触发条件**：创建版本标签 (`v*`)、手动触发
- **功能**：构建多平台版本并自动创建 GitHub Release
- **产物**：Windows、macOS、Linux 安装包

#### 🚀 使用方法

**自动发布版本：**

```bash
# 1. 创建版本标签
git tag v1.0.0
git push origin v1.0.0

# 2. GitHub Actions 将自动构建并创建 Release
```

**手动触发构建：**

1. 进入 GitHub 仓库的 Actions 页面
2. 选择对应的工作流
3. 点击 "Run workflow" 按钮

**开发构建：**

- 推送代码到 `main` 分支将自动触发 Windows 构建
- 创建 PR 将自动触发代码检查和构建验证

#### ⚙️ 配置说明

**代码签名（可选）：**
如需启用 Windows 代码签名，在仓库设置中添加以下 Secrets：

```
CSC_LINK=<证书文件的base64编码>
CSC_KEY_PASSWORD=<证书密码>
```

然后在工作流中启用签名：

```yaml
env:
  CSC_IDENTITY_AUTO_DISCOVERY: true
```

**构建缓存：**

- 使用 pnpm 缓存加速依赖安装
- 自动缓存 node_modules 和构建产物

## 🔧 常见问题

### 安装问题

**Q: pnpm install 失败**

```bash
# 清理缓存后重试
pnpm store prune
pnpm install --frozen-lockfile
```

**Q: Electron 下载缓慢**

```bash
# 设置镜像源
export ELECTRON_MIRROR=https://cdn.npm.taobao.org/dist/electron/
pnpm install
```

### 开发问题

**Q: 热重载不工作**

- 确保开发服务器正常启动
- 检查防火墙是否阻止了端口访问
- 尝试重启开发服务器

**Q: TypeScript 类型错误**

```bash
# 清理类型缓存
rm -rf node_modules/.cache
pnpm typecheck
```

**Q: 环境变量读取不到**

- 确保 `.env` 文件存在且格式正确
- 检查变量名是否以 `VITE_` 开头
- 重启开发服务器使变量生效

### 运行问题

**Q: 千牛连接失败**

- 确保千牛客户端正常运行
- 检查连接器服务是否启动
- 验证网络连接和端口配置

**Q: AI 推荐获取失败**

- 检查后端 API 服务状态
- 验证认证 token 是否有效
- 查看网络请求是否被拦截

**Q: 窗口无法显示**

- 检查屏幕分辨率和多显示器设置
- 尝试重置窗口位置: 删除本地存储数据
- 确保没有其他应用占用快捷键

### 打包问题

**Q: 打包失败**

```bash
# 清理构建缓存
pnpm clean
pnpm build
pnpm build:electron
```

**Q: 打包文件过大**

- 检查是否包含了不必要的依赖
- 启用代码压缩和 Tree Shaking
- 排除开发依赖和测试文件

## 📈 更新日志

### v1.0.0 (2024-07-31)

- ✨ 初始版本发布
- 🤖 AI 智能推荐功能
- 🔄 千牛客户端实时同步
- 📱 智能悬浮窗口
- ⚡ 全局快捷键支持
- 🎨 现代化用户界面

### 计划中的功能

- 📊 数据统计和报表功能
- 🔧 插件系统和扩展支持
- 🌍 多语言界面支持
- 📱 移动端伴侣应用
- 🤖 更多 AI 功能和集成

## 🤝 贡献指南

我们欢迎所有形式的贡献！无论是 bug 报告、功能建议、代码提交还是文档改进。

### 如何贡献

1. **Fork 项目**到你的 GitHub 账户
2. **创建功能分支**: `git checkout -b feature/AmazingFeature`
3. **提交更改**: `git commit -m 'Add some AmazingFeature'`
4. **推送到分支**: `git push origin feature/AmazingFeature`
5. **提交 Pull Request**

### 提交规范

我们使用 [Conventional Commits](https://conventionalcommits.org/) 规范：

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 代码审查

所有的代码提交都需要通过代码审查：

- 遵循项目的代码规范
- 包含必要的测试用例
- 更新相关文档
- 通过 CI/CD 检查

## 📞 技术支持

### 获取帮助

- **文档**: [https://docs.ykwy.com/assistant-client](https://docs.ykwy.com/assistant-client)
- **问题反馈**: [GitHub Issues](https://github.com/ykwy/ykwy-assistant-client/issues)
- **讨论区**: [GitHub Discussions](https://github.com/ykwy/ykwy-assistant-client/discussions)
- **邮箱支持**: <EMAIL>

### 社区

- **QQ 群**: 123456789
- **微信群**: 添加微信号 ykwy-support
- **用户论坛**: [https://forum.ykwy.com](https://forum.ykwy.com)

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议发布。
