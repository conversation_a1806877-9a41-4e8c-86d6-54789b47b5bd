// 桌面端 API 客户端配置
// 直接连接本地后端服务，不使用代理
const API_BASE_URL =
  // import.meta.env.VITE_API_URL || 'https://ykwy-assistant-api.wuyoutansuo.com'
  'http://localhost:3002'

// 用户类型定义
interface User {
  id: string
  username: string
  email?: string
  role?: string
  [key: string]: unknown
}

// 添加调试信息
console.log('🔍 [API Client] 当前API_BASE_URL:', API_BASE_URL)

// 获取存储的认证token (使用 localStorage)
export function getAuthToken(): string | null {
  try {
    const stored = localStorage.getItem('auth-storage')
    if (stored) {
      const data = JSON.parse(stored)
      return data.accessToken || null
    }
  } catch (error) {
    console.error('Failed to get auth token:', error)
  }
  return null
}

// 保存认证token到localStorage
export function saveAuthToken(tokenData: {
  accessToken: string
  refreshToken: string
  expiresIn: number
  user: User
}): void {
  try {
    localStorage.setItem('auth-storage', JSON.stringify(tokenData))
    console.log('✅ [Auth] Token保存成功')
  } catch (error) {
    console.error('❌ [Auth] Token保存失败:', error)
  }
}

// 清除认证token
export function clearAuthToken(): void {
  try {
    localStorage.removeItem('auth-storage')
    console.log('🔄 [Auth] Token已清除')
  } catch (error) {
    console.error('❌ [Auth] Token清除失败:', error)
  }
}

// 自动登录功能
export async function autoLogin(): Promise<boolean> {
  try {
    console.log('🔄 [Auth] 尝试自动登录...')

    // 使用预设的测试账号（可以从环境变量配置）
    const email = import.meta.env.VITE_AUTO_LOGIN_EMAIL || '<EMAIL>'
    const password = import.meta.env.VITE_AUTO_LOGIN_PASSWORD || 'Password123'

    console.log('🔍 [Auth] 使用邮箱:', email)

    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email,
        password
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ [Auth] 自动登录失败:', response.status, errorText)
      return false
    }

    const result = await response.json()
    if (result.success && result.data) {
      saveAuthToken(result.data)
      console.log('✅ [Auth] 自动登录成功:', result.data.user.email)
      return true
    }

    console.error('❌ [Auth] 登录响应格式错误:', result)
    return false
  } catch (error) {
    console.error('❌ [Auth] 自动登录异常:', error)
    return false
  }
}

// 初始化认证 - 检查是否有有效token，没有则自动登录
export async function initializeAuth(): Promise<boolean> {
  console.log('🚀 [Auth] 初始化认证系统...')

  // 检查是否已有有效token
  const existingToken = getAuthToken()
  if (existingToken) {
    console.log('✅ [Auth] 发现现有token，跳过登录')
    return true
  }

  // 没有token，执行自动登录
  console.log('🔄 [Auth] 没有发现token，执行自动登录')
  return await autoLogin()
}

// API 错误类
export class ApiError extends Error {
  public readonly status: number
  public readonly response?: Response
  public readonly code?: string

  constructor(
    message: string,
    status: number,
    response?: Response,
    code?: string
  ) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.response = response
    this.code = code
  }

  get isClientError(): boolean {
    return this.status >= 400 && this.status < 500
  }

  get isServerError(): boolean {
    return this.status >= 500
  }
}

// 请求配置接口
interface ApiRequestConfig extends RequestInit {
  timeout?: number
  retries?: number
  retryDelay?: number
}

// 查询参数类型
type QueryParams = Record<string, string | number | boolean | undefined | null>

// 请求数据类型
type RequestData = Record<string, unknown> | FormData

// 带重试的 fetch
async function fetchWithRetry<T>(
  url: string,
  config: ApiRequestConfig,
  retries: number = 2,
  retryDelay: number = 1000
): Promise<T> {
  let lastError: Error | null = null

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController()
      let timeoutId: NodeJS.Timeout | undefined

      if (config.timeout) {
        timeoutId = setTimeout(() => controller.abort(), config.timeout)
      }

      console.log(
        `🚀 [Desktop API Client] 发起请求 (尝试 ${attempt + 1}/${
          retries + 1
        }):`,
        {
          url,
          method: config.method || 'GET',
          hasBody: !!config.body
        }
      )

      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      })

      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      console.log(`📥 [Desktop API Client] 收到响应:`, {
        url,
        status: response.status,
        statusText: response.statusText,
        attempt: attempt + 1
      })

      // 处理401认证错误
      if (response.status === 401) {
        console.log('🔒 [Auth] 检测到401错误，尝试自动登录...')

        // 清除旧token
        clearAuthToken()

        // 尝试自动登录
        const loginSuccess = await autoLogin()

        if (loginSuccess && attempt < retries) {
          console.log('🔄 [Auth] 登录成功，重试原请求...')

          // 更新请求头中的token
          const newToken = getAuthToken()
          if (newToken && config.headers) {
            ;(config.headers as Record<string, string>)[
              'Authorization'
            ] = `Bearer ${newToken}`
          }

          // 延迟后重试
          await new Promise(resolve => setTimeout(resolve, 1000))
          continue
        } else {
          throw new ApiError(
            'Authentication failed',
            401,
            response,
            'AUTH_FAILED'
          )
        }
      }

      if (!response.ok) {
        await response.text() // 消费响应体
        throw new ApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          response,
          'HTTP_ERROR'
        )
      }

      const contentType = response.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        const data = await response.json()
        console.log(`✅ [Desktop API Client] JSON响应成功:`, {
          url,
          dataKeys: Object.keys(data || {}),
          attempt: attempt + 1
        })
        return data
      } else {
        const text = await response.text()
        console.log(`✅ [Desktop API Client] 文本响应成功:`, {
          url,
          textLength: text.length,
          attempt: attempt + 1
        })
        return text as T
      }
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))

      console.log(
        `❌ [Desktop API Client] 请求失败 (尝试 ${attempt + 1}/${
          retries + 1
        }):`,
        {
          url,
          error: lastError.message,
          willRetry: attempt < retries
        }
      )

      if (attempt < retries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        continue
      }
    }
  }

  throw new ApiError('Network error', 0, undefined, 'NETWORK_ERROR')
}

async function fetchApi<T>(
  endpoint: string,
  options: ApiRequestConfig = {}
): Promise<T> {
  // 确保endpoint以/开头
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  const url = `${API_BASE_URL}/api/v1${cleanEndpoint}`

  // 调试日志
  console.log('🔍 [Desktop API Client] 构造的 URL:', url)
  console.log('🔍 [Desktop API Client] endpoint:', endpoint)
  console.log('🔍 [Desktop API Client] cleanEndpoint:', cleanEndpoint)
  console.log('🔍 [Desktop API Client] API_BASE_URL:', API_BASE_URL)

  // 获取认证token并添加到请求头
  const token = getAuthToken()

  console.log('🔍 [Desktop API Client] 认证token存在:', !!token)
  if (token) {
    console.log('🔍 [Desktop API Client] 添加Authorization头')
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...((options.headers as Record<string, string>) || {})
  }

  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  const config: ApiRequestConfig = {
    headers,
    credentials: 'include', // 包含认证cookie
    timeout: 1000 * 60 * 3,
    retries: 2,
    retryDelay: 1000,
    ...options
  }

  return fetchWithRetry<T>(url, config, config.retries, config.retryDelay)
}

export const apiClient = {
  get: <T>(
    endpoint: string,
    params?: QueryParams,
    config?: ApiRequestConfig
  ) => {
    console.log('🔍 [Desktop API Client GET] 原始 endpoint:', endpoint)
    console.log('🔍 [Desktop API Client GET] 查询参数:', params)

    return fetchApi<T>(endpoint, { ...config, method: 'GET' })
  },

  post: <T>(endpoint: string, data?: RequestData, config?: ApiRequestConfig) =>
    fetchApi<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...config
    }),

  put: <T>(endpoint: string, data?: RequestData, config?: ApiRequestConfig) =>
    fetchApi<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...config
    }),

  patch: <T>(endpoint: string, data?: RequestData, config?: ApiRequestConfig) =>
    fetchApi<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...config
    }),

  delete: <T>(endpoint: string, config?: ApiRequestConfig) =>
    fetchApi<T>(endpoint, { method: 'DELETE', ...config }),

  // 千牛API相关方法
  qianniu: {
    // 发送文本到输入框
    insertTextToInputbox: async (params: {
      connectionId: string
      nickname: string
      text: string
    }): Promise<{
      success: boolean
      message: string
      data?: unknown
      timestamp: string
    }> => {
      console.log('🔍 [千牛API] 发送文本到输入框:', params)
      return fetchApi('/qianniu-api/insert-text-to-inputbox', {
        method: 'POST',
        body: JSON.stringify(params)
      })
    }
  }
}
