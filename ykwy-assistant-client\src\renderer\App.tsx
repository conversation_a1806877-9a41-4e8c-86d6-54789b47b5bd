import React, { useState, useEffect } from 'react'
import { Pin, <PERSON>nO<PERSON>, <PERSON><PERSON><PERSON>, Minus, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { AssistantPanel } from '@/components/assistant-panel'
import { initializeAuth } from '@/lib/api-client'
import { useQianNiuMonitor } from '@/hooks/use-qianniu-monitor'

// 扩展CSSProperties类型以支持webkitAppRegion
interface ExtendedCSSProperties extends React.CSSProperties {
  webkitAppRegion?: 'drag' | 'no-drag'
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface AppProps {
  // 应用属性接口，目前为空但预留扩展
}

export default function App(_props: AppProps) {
  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(true)
  const [, setIsVisible] = useState(true)
  const [, setAuthInitialized] = useState(false)

  // 获取千牛连接状态
  const qianNiuStatus = useQianNiuMonitor()

  // 应用启动时初始化认证
  useEffect(() => {
    const init = async () => {
      console.log('🚀 [App] 开始应用初始化...')

      // 调试环境变量
      console.log('🔍 [Env Debug] All import.meta.env:', import.meta.env)

      try {
        const authSuccess = await initializeAuth()
        if (authSuccess) {
          console.log('✅ [App] 认证初始化成功')
        } else {
          console.warn('⚠️ [App] 认证初始化失败，但应用将继续运行')
        }
      } catch (error) {
        console.error('❌ [App] 认证初始化异常:', error)
      } finally {
        setAuthInitialized(true)
        console.log('✅ [App] 应用初始化完成')
      }
    }

    init()
  }, [])

  // 检查窗口可见性
  useEffect(() => {
    console.log('🔍 [App Debug] window.electronAPI:', window.electronAPI)

    const checkVisibility = async () => {
      if (window.electronAPI) {
        try {
          const visible = await window.electronAPI.window?.isVisible()
          console.log('🔍 [App Debug] Window visibility:', visible)
          if (visible !== undefined) {
            setIsVisible(visible)
          }
        } catch (error) {
          console.error('🔍 [App Debug] Error checking visibility:', error)
        }
      } else {
        console.warn('🔍 [App Debug] window.electronAPI is not available')
      }
    }

    checkVisibility()

    // 定期检查窗口状态
    const interval = setInterval(checkVisibility, 1000)
    return () => clearInterval(interval)
  }, [])

  // 窗口控制函数
  const handleMinimize = async () => {
    if (window.electronAPI) {
      await window.electronAPI.window?.minimize()
    }
  }

  const handleClose = async () => {
    if (window.electronAPI) {
      await window.electronAPI.window?.close()
    }
  }

  const toggleAlwaysOnTop = async () => {
    if (window.electronAPI) {
      const newState = !isAlwaysOnTop
      await window.electronAPI.window?.setAlwaysOnTop(newState)
      setIsAlwaysOnTop(newState)
    }
  }

  return (
    <div className="h-screen w-full flex flex-col bg-background text-foreground glass overflow-hidden shadow-2xl">
      {/* 自定义标题栏 */}
      <div className="window-drag flex items-center justify-between h-12 px-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
            <span className="text-xs font-bold">AI</span>
          </div>
          <h1 className="text-sm font-semibold">易康无忧客服助手</h1>
        </div>

        <div
          className="window-no-drag flex items-center gap-1 relative z-50"
          style={
            {
              pointerEvents: 'auto',
              webkitAppRegion: 'no-drag'
            } as ExtendedCSSProperties
          }
        >
          {/* 置顶按钮 */}
          <button
            onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 置顶按钮被点击')
              toggleAlwaysOnTop()
            }}
            onMouseDown={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 置顶按钮 mousedown')
            }}
            className={cn(
              'p-1.5 rounded hover:bg-white/20 transition-colors cursor-pointer select-none',
              isAlwaysOnTop && 'bg-white/30'
            )}
            title={isAlwaysOnTop ? '取消置顶' : '窗口置顶'}
            style={
              {
                pointerEvents: 'auto',
                webkitAppRegion: 'no-drag',
                zIndex: 1000
              } as ExtendedCSSProperties
            }
          >
            {isAlwaysOnTop ? (
              <PinOff className="w-4 h-4" />
            ) : (
              <Pin className="w-4 h-4" />
            )}
          </button>

          {/* 设置按钮 */}
          <button
            onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 设置按钮被点击')
            }}
            onMouseDown={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 设置按钮 mousedown')
            }}
            className="p-1.5 rounded hover:bg-white/20 transition-colors cursor-pointer select-none"
            title="设置"
            style={{
              pointerEvents: 'auto',
              webkitAppRegion: 'no-drag',
              zIndex: 1000
            }}
          >
            <Settings className="w-4 h-4" />
          </button>

          {/* 最小化按钮 */}
          <button
            onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 最小化按钮被点击')
              handleMinimize()
            }}
            onMouseDown={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 最小化按钮 mousedown')
            }}
            className="p-1.5 rounded hover:bg-white/20 transition-colors cursor-pointer select-none"
            title="最小化"
            style={{
              pointerEvents: 'auto',
              webkitAppRegion: 'no-drag',
              zIndex: 1000
            }}
          >
            <Minus className="w-4 h-4" />
          </button>

          {/* 关闭按钮 */}
          <button
            onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 关闭按钮被点击')
              handleClose()
            }}
            onMouseDown={e => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔘 关闭按钮 mousedown')
            }}
            className="p-1.5 rounded hover:bg-red-500 transition-colors cursor-pointer select-none"
            title="关闭"
            style={{
              pointerEvents: 'auto',
              webkitAppRegion: 'no-drag',
              zIndex: 1000
            }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 overflow-hidden">
        <AssistantPanel />
      </div>

      {/* 状态栏 */}
      <div className="h-6 px-4 bg-muted/50 border-t border-border flex items-center justify-between text-xs text-muted-foreground">
        <span>就绪</span>
        <div className="flex items-center gap-2">
          <span
            className={cn(
              'w-2 h-2 rounded-full',
              qianNiuStatus.isConnected ? 'bg-green-500' : 'bg-red-500'
            )}
          ></span>
          <span>{qianNiuStatus.isConnected ? '在线' : '离线'}</span>
        </div>
      </div>
    </div>
  )
}
