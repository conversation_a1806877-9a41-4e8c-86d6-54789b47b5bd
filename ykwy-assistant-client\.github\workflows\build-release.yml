name: 构建和发布应用

on:
  # 创建标签时触发（用于版本发布）
  push:
    tags:
      - 'v*'
  # 手动触发
  workflow_dispatch:
    inputs:
      release_type:
        description: '发布类型'
        required: true
        default: 'draft'
        type: choice
        options:
          - draft
          - prerelease
          - release

jobs:
  build:
    strategy:
      matrix:
        include:
          - os: windows-latest
            platform: win
            arch: x64
    runs-on: ${{ matrix.os }}
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
      - name: 设置 Node.js 环境
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          registry-url: 'https://registry.npmjs.org'
      - name: 安装 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest
      - name: 获取 pnpm store 目录
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
      - name: 设置 pnpm 缓存
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
      - name: 设置 Electron 缓存
        uses: actions/cache@v4
        with:
          path: ~/.cache/electron
          key: ${{ runner.os }}-electron-cache-${{ hashFiles('**/package.json') }}
          restore-keys: |
            ${{ runner.os }}-electron-cache-
      - name: 清除 Electron 缓存
        shell: bash
        run: |
          echo "清除 Electron 缓存"
          rm -rf "$HOME/.cache/electron"
      - name: 安装依赖
        shell: bash
        run: |
          if [ -f .npmrc ]; then
            echo "删除现有的 .npmrc 文件"
            rm .npmrc
          fi
          npm config set registry https://registry.npmjs.org/
          npm cache clean --force
          pnpm config set registry https://registry.npmjs.org/
          npm config list
          pnpm config list
          pnpm install --no-frozen-lockfile --registry=https://registry.npmjs.org/
      - name: 类型检查
        run: pnpm typecheck
      - name: 构建应用 (Windows)
        run: pnpm build:win
        env:
          CSC_IDENTITY_AUTO_DISCOVERY: false
          ELECTRON_MIRROR: https://npmmirror.com/mirrors/electron/
          NPM_CONFIG_REGISTRY: https://registry.npmjs.org/
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/') || github.event_name == 'workflow_dispatch'
    permissions:
      contents: write
      discussions: write
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
      - name: 安装 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest
      - name: 下载所有构建产物
        if: github.event_name == 'workflow_dispatch' && (github.event.inputs.release_type == 'prerelease' || github.event.inputs.release_type == 'release')
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      - name: 准备发布文件
        if: github.event_name == 'workflow_dispatch' && (github.event.inputs.release_type == 'prerelease' || github.event.inputs.release_type == 'release')
        run: |
          mkdir -p release
          find artifacts -type f \( -name "*.exe" -o -name "*.yml" \) -exec cp {} release/ \;
          ls -la release/
      - name: 获取标签名
        id: get_tag
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          else
            echo "tag=v$(date +'%Y%m%d-%H%M%S')" >> $GITHUB_OUTPUT
          fi
      - name: 创建 Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ steps.get_tag.outputs.tag }}
          name: 易康无忧客服助手 ${{ steps.get_tag.outputs.tag }}
          draft: ${{ github.event.inputs.release_type == 'draft' || github.event.inputs.release_type == '' }}
          prerelease: ${{ github.event.inputs.release_type == 'prerelease' }}
          files: |
            release/*.exe
            release/*.yml
          body: |
            ## 更新内容

            ### 新功能
            - 请在此添加新功能描述

            ### 修复
            - 请在此添加修复内容

            ### 改进
            - 请在此添加改进内容

            ---

            ### 下载说明
            - Windows 用户请下载 `.exe` 文件

            ### 安装说明
            - Windows: 双击运行 `.exe` 文件进行安装
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
