import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { apiClient } from '@/lib/api-client'
import { normalizeConversationCode } from '@/lib/utils'

// AI推荐类型
interface AIRecommendation {
  id: string
  content: string
  confidence: number
  category: string
}

// 获取AI推荐的hook
export const useAIRecommendations = (
  conversationId: string | null,
  enabled: boolean = true
) => {
  const queryClient = useQueryClient()

  // 使用统一标准化函数处理conversationCode
  const conversationCode = normalizeConversationCode(conversationId)

  const queryResult = useQuery({
    queryKey: ['aiRecommendations', conversationCode],
    queryFn: async (): Promise<AIRecommendation[]> => {
      if (!conversationCode) return []

      try {
        console.log('🔍 [AI推荐] 原始conversationId:', conversationId)
        console.log('🔍 [AI推荐] 标准化conversationCode:', conversationCode)

        const response = await apiClient.get<{
          recommendations: string[]
          conversation?: {
            id: string
            conversationCode: string
            customerNickname: string
            messageCount: number
          }
        }>(`/salesAgent/recommendations/conversation/${conversationCode}`)

        // 将字符串数组转换为 AIRecommendation 对象数组
        return (response.recommendations || []).map(
          (content: string, index: number): AIRecommendation => ({
            id: `${conversationCode}-ai-rec-${Date.now()}-${index}`,
            content,
            confidence: 90 - index * 5, // 模拟置信度递减
            category: '销售智能体推荐'
          })
        )
      } catch (error) {
        console.error('获取AI推荐失败:', error)
        throw error
      }
    },
    enabled: !!conversationCode && enabled,
    staleTime: 30000, // 30秒内的数据被视为新鲜的
    gcTime: 5 * 60 * 1000, // 5分钟缓存时间
    refetchOnWindowFocus: false, // 窗口聚焦时不自动重新获取
    retry: 2 // 最多重试2次
  })

  // 监听conversationId变化，自动刷新
  useEffect(() => {
    if (conversationCode) {
      console.log('🔄 [AI推荐] conversationId变化，准备刷新:', conversationCode)

      // 直接执行缓存失效，移除延迟避免竞态条件
      queryClient.invalidateQueries({
        queryKey: ['aiRecommendations', conversationCode],
        exact: true,
        refetchType: 'active'
      })
    }
  }, [conversationId, conversationCode, queryClient])

  return queryResult
}
