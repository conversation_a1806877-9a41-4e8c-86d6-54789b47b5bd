import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  Send,
  Refresh<PERSON><PERSON>,
  <PERSON>ader2,
  <PERSON>ertCircle,
  Settings2,
  Toggle<PERSON><PERSON>t,
  ToggleRight,
  Edit3
} from 'lucide-react'
import { useQueryClient } from '@tanstack/react-query'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useAIRecommendations } from '@/hooks/use-ai-recommendations'
import { useSendMessage } from '@/hooks/use-send-message'
import { useQianNiuMonitor } from '@/hooks/use-qianniu-monitor'
import { useAutoReply } from '@/hooks/use-auto-reply'
import { useInsertTextToInputbox } from '@/hooks/use-insert-text-to-inputbox'
import { normalizeConversationCode, isSameConversation } from '@/lib/utils'

interface Recommendation {
  id: string
  content: string
  confidence: number
  category: string
}

// 平台连接类型
interface PlatformConnection {
  id: string
  clientId: string
  connectionId: string
  isOnline: boolean
  currentUser?: {
    id: string
    nickname: string
    avatar?: string
    platformCustomerId: string
    conversationId: string
    lastActivity: string
  }
  [key: string]: unknown
}

// 客户端账户类型
interface ClientAccount {
  accountName: string
  shopName: string
  platformType: string
  conversations: Array<{
    id: string
    title: string
    conversationCode?: string
    lastActivity: string
    customer: {
      nickname: string
      avatar?: string
    }
  }>
}

// 在线客户端类型
interface OnlineClient {
  id: string
  name: string
  connectionId: string
  accounts: ClientAccount[]
}

// 统一的用户信息类型
interface UserInfo {
  nickname: string
  avatar?: string
  platformCustomerId?: string
  conversationId?: string
  id?: string
  lastActivity?: string
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface AssistantPanelProps {
  // 组件属性接口，目前为空但预留扩展
}

export function AssistantPanel(_props: AssistantPanelProps) {
  const [selectedRecommendation, setSelectedRecommendation] = useState<
    string | null
  >(null)
  const [isConnected, setIsConnected] = useState(false)
  const [currentConversationId, setCurrentConversationId] = useState<
    string | null
  >(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isSending, setIsSending] = useState(false) // 添加发送状态锁

  const queryClient = useQueryClient()

  // 监听千牛客户端状态和会话切换
  const qianNiuStatus = useQianNiuMonitor()
  console.log('🎯 [AssistantPanel] 千牛状态:', qianNiuStatus)

  // 获取 AI 推荐
  const {
    data: recommendations,
    isLoading: recommendationsLoading,
    error: recommendationsError,
    refetch: refetchRecommendations
  } = useAIRecommendations(currentConversationId, !!currentConversationId)

  // 自动回复管理
  const {
    autoReplyEnabled,
    isLoading: autoReplyLoading,
    toggleAutoReply
  } = useAutoReply(currentConversationId)

  // 发送消息 mutation
  const sendMessageMutation = useSendMessage()

  // 发送文本到输入框 mutation
  const insertTextMutation = useInsertTextToInputbox()

  // 监听千牛状态变化
  useEffect(() => {
    console.log('🎯 [AssistantPanel] 状态更新:', {
      qianNiuStatus,
      currentConversationId: qianNiuStatus?.currentConversationId,
      isConnected: qianNiuStatus?.isConnected,
      activeClients: qianNiuStatus?.activeClients
    })

    // 使用标准化的conversationCode比较，避免格式差异导致的误判
    const currentStandardCode = normalizeConversationCode(currentConversationId)
    const newStandardCode = normalizeConversationCode(
      qianNiuStatus?.currentConversationId
    )

    if (
      !isSameConversation(
        currentConversationId,
        qianNiuStatus?.currentConversationId
      )
    ) {
      const newConversationId = qianNiuStatus?.currentConversationId || null

      console.log('🔄 [AssistantPanel] 会话ID变化:', {
        从: currentConversationId,
        到: newConversationId,
        标准化前: currentStandardCode,
        标准化后: newStandardCode
      })

      // 更新当前对话ID
      setCurrentConversationId(newConversationId)
      setSelectedRecommendation(null)

      // 当对话切换时，主动触发相关数据的刷新
      if (newStandardCode) {
        console.log('🔄 [AssistantPanel] 对话切换，准备刷新数据:', {
          conversationId: newConversationId,
          conversationCode: newStandardCode
        })

        try {
          // 清理和刷新AI推荐缓存
          queryClient.invalidateQueries({
            queryKey: ['aiRecommendations', newStandardCode],
            exact: true,
            refetchType: 'active'
          })

          // 同时刷新对话相关的其他缓存（如消息列表）
          queryClient.invalidateQueries({
            queryKey: ['messages', newStandardCode],
            exact: true,
            refetchType: 'active'
          })

          console.log('✅ [AssistantPanel] 对话切换缓存刷新已调度')
        } catch (error) {
          console.error('❌ [AssistantPanel] 对话切换缓存刷新失败:', error)
        }
      }

      // useAIRecommendations中的useEffect也会处理缓存刷新，形成双重保障
    }

    const newIsConnected = qianNiuStatus?.isConnected || false
    if (newIsConnected !== isConnected) {
      console.log('🔄 [AssistantPanel] 连接状态变化:', {
        从: isConnected,
        到: newIsConnected
      })
      setIsConnected(newIsConnected)
    }
  }, [qianNiuStatus, currentConversationId, isConnected, queryClient])

  // 定期检查千牛状态
  useEffect(() => {
    const interval = setInterval(() => {
      qianNiuStatus?.refetch?.()
    }, 3000) // 每3秒检查一次

    return () => clearInterval(interval)
  }, [qianNiuStatus])

  // 处理推荐选择
  const handleSelectRecommendation = (content: string) => {
    setSelectedRecommendation(content)
  }

  // 处理发送到输入框
  const handleInsertToInputbox = async (
    content: string,
    event?: React.MouseEvent
  ) => {
    if (event) {
      event.preventDefault()
      event.stopPropagation()
      ;(
        event as React.MouseEvent & { stopImmediatePropagation?: () => void }
      ).stopImmediatePropagation?.()
    }

    if (!content) {
      console.warn('⚠️ [AssistantPanel] 无法发送到输入框: 缺少内容')
      return
    }

    // 从 connectionInfo.onlineClients 获取连接信息
    console.log('🔍 [AssistantPanel] 调试qianNiuStatus:', {
      qianNiuStatus,
      hasConnectionInfo: !!qianNiuStatus?.connectionInfo,
      hasOnlineClients: !!qianNiuStatus?.connectionInfo?.onlineClients,
      onlineClientsLength: qianNiuStatus?.connectionInfo?.onlineClients?.length
    })

    if (
      !qianNiuStatus?.connectionInfo?.onlineClients ||
      !Array.isArray(qianNiuStatus.connectionInfo.onlineClients)
    ) {
      console.warn('⚠️ [AssistantPanel] 无法发送到输入框: 缺少在线客户端信息', {
        qianNiuStatus,
        connectionInfo: qianNiuStatus?.connectionInfo
      })
      return
    }

    // 查找有账户和会话的在线客户端
    const activeClient = qianNiuStatus.connectionInfo.onlineClients.find(
      (client: {
        connectionId: string
        accounts: Array<{
          conversations: Array<{
            customer: {
              nickname: string
            }
          }>
        }>
      }) =>
        client.accounts &&
        client.accounts.length > 0 &&
        client.accounts.some(
          account => account.conversations && account.conversations.length > 0
        )
    )

    if (!activeClient) {
      console.warn('⚠️ [AssistantPanel] 无法发送到输入框: 没有找到活跃的客户端')
      return
    }

    const connectionId = activeClient.connectionId

    // 从第一个有会话的账户中获取客户昵称
    const firstAccount = activeClient.accounts.find(
      account => account.conversations && account.conversations.length > 0
    )
    const nickname = firstAccount?.conversations[0]?.customer?.nickname

    if (!nickname) {
      console.warn('⚠️ [AssistantPanel] 无法发送到输入框: 缺少用户昵称')
      return
    }

    try {
      console.log('🚀 [AssistantPanel] 开始发送到输入框', {
        nickname,
        content: content.slice(0, 50) + '...',
        connectionId
      })

      await insertTextMutation.mutateAsync({
        connectionId,
        nickname,
        text: content
      })

      console.log('✅ [AssistantPanel] 文本已发送到输入框')
    } catch (error) {
      console.error('❌ [AssistantPanel] 发送到输入框失败:', error)
    }
  }

  // 直接发送消息（不依赖selectedRecommendation状态）
  const handleDirectSendMessage = async (
    content: string,
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    if (!content || !currentConversationId) {
      console.warn('⚠️ [AssistantPanel] 无法发送消息: 缺少必要参数', {
        hasContent: !!content,
        hasCurrentConversationId: !!currentConversationId
      })
      return
    }

    // 防止重复发送
    if (isSending || sendMessageMutation.isPending) {
      console.warn('⚠️ [AssistantPanel] 消息正在发送中，跳过重复请求')
      return
    }

    // 阻止事件冒泡
    event.preventDefault()
    event.stopPropagation()
    ;(
      event as React.MouseEvent & { stopImmediatePropagation?: () => void }
    ).stopImmediatePropagation?.()

    // 设置发送状态锁
    setIsSending(true)

    try {
      console.log('🚀 [AssistantPanel] 开始直接发送消息', {
        conversationId: currentConversationId,
        content: content.slice(0, 50) + '...',
        timestamp: new Date().toISOString()
      })

      await sendMessageMutation.mutateAsync({
        conversationId: currentConversationId,
        content: content
      })

      console.log('✅ [AssistantPanel] 消息发送成功，等待AI推荐刷新')
    } catch (error) {
      console.error('❌ [AssistantPanel] 消息发送失败:', error)
    } finally {
      // 无论成功还是失败，都要释放发送状态锁
      setIsSending(false)
    }
  }

  // 手动刷新推荐
  const handleRefreshRecommendations = async (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault()
      event.stopPropagation()
      // 类型断言，因为React.MouseEvent可能没有stopImmediatePropagation
      ;(
        event as React.MouseEvent & { stopImmediatePropagation?: () => void }
      ).stopImmediatePropagation?.()
    }

    const conversationCode = normalizeConversationCode(currentConversationId)

    if (conversationCode && !isRefreshing) {
      console.log('🔄 [AssistantPanel] 手动刷新推荐')
      setIsRefreshing(true)

      try {
        console.log(
          '🔄 [AssistantPanel] 使用标准化conversationCode刷新:',
          conversationCode
        )

        // 使用温和的缓存刷新策略
        queryClient.invalidateQueries({
          queryKey: ['aiRecommendations', conversationCode],
          exact: true,
          refetchType: 'active'
        })

        // 同时触发refetch作为备选
        await refetchRecommendations()

        // 确保加载状态至少持续800ms，给用户足够的视觉反馈
        setTimeout(() => {
          setIsRefreshing(false)
        }, 800)
      } catch (error) {
        console.error('❌ [AssistantPanel] 手动刷新失败:', error)
        setIsRefreshing(false)
      }
    }
  }

  // 渲染推荐卡片
  const renderRecommendationCard = (
    recommendation: Recommendation,
    index: number
  ) => (
    <div
      key={recommendation.id}
      className={cn(
        'p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md',
        selectedRecommendation === recommendation.content
          ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-950/50'
          : 'border-border bg-card hover:border-indigo-300'
      )}
      onClick={() => handleSelectRecommendation(recommendation.content)}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <div
            className={cn(
              'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',
              selectedRecommendation === recommendation.content
                ? 'bg-indigo-600 text-white'
                : 'bg-muted text-muted-foreground'
            )}
          >
            {index + 1}
          </div>
          <span className="text-xs text-muted-foreground font-medium">
            {recommendation.category}
          </span>
        </div>
        <div className="flex items-center gap-1">
          <span
            className={cn(
              'text-xs font-medium',
              recommendation.confidence >= 90
                ? 'text-green-600'
                : recommendation.confidence >= 80
                ? 'text-yellow-600'
                : 'text-gray-600'
            )}
          >
            {recommendation.confidence}%
          </span>
        </div>
      </div>

      <p className="text-sm text-foreground leading-relaxed mb-3">
        {recommendation.content}
      </p>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {selectedRecommendation === recommendation.content && (
            <span className="text-xs text-indigo-600 font-medium">已选择</span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            className="h-7 px-2 text-xs"
            onClick={e => handleInsertToInputbox(recommendation.content, e)}
            disabled={insertTextMutation.isPending}
          >
            {insertTextMutation.isPending ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <Edit3 className="w-3 h-3" />
            )}
            <span className="ml-1">输入框</span>
          </Button>
          <Button
            size="sm"
            variant="default"
            className="h-7 px-2 text-xs"
            onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              handleDirectSendMessage(recommendation.content, e)
            }}
            disabled={isSending || sendMessageMutation.isPending}
          >
            {isSending || sendMessageMutation.isPending ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <Send className="w-3 h-3" />
            )}
            <span className="ml-1">发送</span>
          </Button>
        </div>
      </div>
    </div>
  )

  // 渲染骨架屏
  const renderSkeleton = () => (
    <div className="space-y-4">
      {[1, 2, 3].map(i => (
        <div
          key={i}
          className="p-4 border border-border rounded-lg animate-pulse"
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-muted rounded-full"></div>
              <div className="h-4 w-16 bg-muted rounded"></div>
            </div>
            <div className="h-4 w-8 bg-muted rounded"></div>
          </div>
          <div className="space-y-2 mb-3">
            <div className="h-4 w-full bg-muted rounded"></div>
            <div className="h-4 w-4/5 bg-muted rounded"></div>
            <div className="h-4 w-3/5 bg-muted rounded"></div>
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <div className="h-full flex flex-col bg-background">
      {/* 标题栏 */}
      <div className="flex-shrink-0 p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-indigo-600" />
            <h2 className="font-semibold text-foreground">AI 智能助手</h2>
            {(recommendationsLoading || isRefreshing) && (
              <Loader2 className="w-4 h-4 text-indigo-600 animate-spin" />
            )}
          </div>

          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <div
                className={cn(
                  'w-2 h-2 rounded-full',
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                )}
              ></div>
              <span className="text-xs text-muted-foreground">
                {isConnected ? '已连接' : '未连接'}
              </span>
            </div>

            {/* 自动回复切换按钮 */}
            {currentConversationId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={e => {
                  e.preventDefault()
                  e.stopPropagation()
                  toggleAutoReply()
                }}
                disabled={autoReplyLoading}
                type="button"
                className={cn(
                  'flex items-center gap-1 px-2',
                  autoReplyEnabled
                    ? 'text-green-600 hover:text-green-700'
                    : 'text-gray-500 hover:text-gray-600'
                )}
                title={`点击${autoReplyEnabled ? '关闭' : '开启'}自动回复`}
              >
                {autoReplyLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : autoReplyEnabled ? (
                  <ToggleRight className="w-4 h-4" />
                ) : (
                  <ToggleLeft className="w-4 h-4" />
                )}
                <span className="text-xs">
                  {autoReplyEnabled ? 'AI自动' : '手动回复'}
                </span>
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={e => {
                e.preventDefault()
                e.stopPropagation()
                handleRefreshRecommendations(e)
              }}
              disabled={!currentConversationId || isRefreshing}
              type="button"
            >
              <RefreshCw
                className={cn(
                  'w-4 h-4',
                  (recommendationsLoading || isRefreshing) && 'animate-spin'
                )}
              />
            </Button>
          </div>
        </div>

        {/* 会话信息 */}
        <div className="mt-2 text-xs text-muted-foreground">
          {!isConnected ? (
            <span>等待千牛客户端连接...</span>
          ) : currentConversationId ? (
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span>
                  当前会话:{' '}
                  {currentConversationId.split('#')[0] ||
                    currentConversationId.slice(-8)}
                </span>
                {/* 自动回复状态指示器 */}
                <div className="flex items-center gap-1">
                  <div
                    className={cn(
                      'w-1.5 h-1.5 rounded-full',
                      autoReplyEnabled ? 'bg-green-500' : 'bg-gray-400'
                    )}
                  ></div>
                  <span
                    className={cn(
                      'text-xs',
                      autoReplyEnabled ? 'text-green-600' : 'text-gray-500'
                    )}
                  >
                    {autoReplyEnabled ? 'AI自动回复' : '手动回复'}
                  </span>
                </div>
              </div>
              {(() => {
                // 尝试从多个数据源获取用户信息
                let displayUser: UserInfo | null = null

                // 方法1: 从 platformConnections 中查找 (期望的主要数据源)
                const currentUserFromPlatform =
                  qianNiuStatus?.platformConnections?.find(
                    (conn: PlatformConnection) =>
                      conn.isOnline && conn.currentUser
                  )?.currentUser

                // 方法2: 如果 qianNiuStatus 直接是数组格式 (根据用户提供的数据)
                const currentUserFromArray = Array.isArray(qianNiuStatus)
                  ? (qianNiuStatus as PlatformConnection[]).find(
                      (conn: PlatformConnection) =>
                        conn.isOnline && conn.currentUser
                    )?.currentUser
                  : null

                // 方法3: 如果数据在 data 字段中 (根据用户API返回)
                const qianNiuWithData = qianNiuStatus as {
                  data?: PlatformConnection[]
                }
                const currentUserFromData = qianNiuWithData?.data
                  ? Array.isArray(qianNiuWithData.data)
                    ? qianNiuWithData.data.find(
                        (conn: PlatformConnection) =>
                          conn.isOnline && conn.currentUser
                      )?.currentUser
                    : null
                  : null

                // 方法4: 从 connectionMappings 中查找 (根据实际数据结构)
                const connectionMappings =
                  qianNiuStatus?.connectionInfo?.connectionMappings
                const mappingEntry = connectionMappings
                  ? Object.values(connectionMappings).find(
                      (mapping: { currentConversationId?: string }) =>
                        mapping.currentConversationId === currentConversationId
                    )
                  : null

                const currentUserFromMappings = mappingEntry
                  ? {
                      nickname: mappingEntry.customerName,
                      platformCustomerId:
                        mappingEntry.currentConversationId?.split('.')[0],
                      conversationId: mappingEntry.currentConversationId
                    }
                  : null

                // 方法5: 从 connectionInfo.onlineClients 中查找（修复匹配逻辑）
                const fallbackCustomer =
                  qianNiuStatus?.connectionInfo?.onlineClients
                    ?.flatMap((client: OnlineClient) => client.accounts || [])
                    ?.flatMap(
                      (account: ClientAccount) => account.conversations || []
                    )
                    ?.find(
                      (conv: {
                        conversationCode?: string
                        title: string
                        id: string
                        customer: { nickname: string; avatar?: string }
                      }) =>
                        conv.conversationCode === currentConversationId || // 主要匹配字段
                        conv.title === currentConversationId ||
                        conv.id === currentConversationId
                    )?.customer

                // 按优先级选择用户信息
                displayUser = (currentUserFromPlatform ||
                  currentUserFromArray ||
                  currentUserFromData ||
                  currentUserFromMappings ||
                  fallbackCustomer) as UserInfo | null

                console.log('🎯 [AssistantPanel] 用户信息调试 (修复后):', {
                  currentConversationId,
                  currentUserFromPlatform,
                  currentUserFromArray,
                  currentUserFromData,
                  currentUserFromMappings,
                  fallbackCustomer,
                  finalDisplayUser: displayUser,
                  connectionMappings,
                  mappingEntry,
                  platformConnections: qianNiuStatus?.platformConnections,
                  qianNiuStatusType: typeof qianNiuStatus,
                  qianNiuStatusIsArray: Array.isArray(qianNiuStatus),
                  qianNiuStatusHasData: !!(qianNiuStatus as { data?: unknown })
                    ?.data,
                  onlineClientsConversations:
                    qianNiuStatus?.connectionInfo?.onlineClients
                      ?.flatMap((client: OnlineClient) => client.accounts || [])
                      ?.flatMap(
                        (account: ClientAccount) => account.conversations || []
                      )
                })

                return displayUser ? (
                  <div className="flex items-center gap-2 text-xs">
                    <span className="text-muted-foreground">当前客户:</span>
                    <div className="flex items-center gap-1">
                      {displayUser.avatar && (
                        <img
                          src={displayUser.avatar}
                          alt="客户头像"
                          className="w-4 h-4 rounded-full"
                        />
                      )}
                      <span className="font-medium text-primary">
                        {displayUser.nickname ||
                          displayUser.platformCustomerId ||
                          '未知客户'}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground text-xs">
                    暂无客户信息 (检查控制台调试信息)
                  </div>
                )
              })()}
            </div>
          ) : (
            <span>千牛已连接，等待进入对话...</span>
          )}
        </div>
      </div>

      {/* 推荐列表 */}
      <div className="flex-1 overflow-y-auto scrollbar-thin">
        <div className="p-4">
          {recommendationsLoading || isRefreshing ? (
            renderSkeleton()
          ) : recommendationsError ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <AlertCircle className="w-12 h-12 text-red-300 mb-4" />
              <h3 className="text-sm font-medium text-red-600 mb-2">
                获取推荐失败
              </h3>
              <p className="text-xs text-red-500 mb-4">
                {recommendationsError instanceof Error
                  ? recommendationsError.message
                  : '请检查网络连接'}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshRecommendations}
              >
                重试
              </Button>
            </div>
          ) : !currentConversationId ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Settings2 className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-sm font-medium text-muted-foreground mb-2">
                等待连接
              </h3>
              <p className="text-xs text-muted-foreground">
                请打开千牛客户端并选择一个会话
              </p>
            </div>
          ) : recommendations && recommendations.length > 0 ? (
            <div className="space-y-4">
              {recommendations
                .slice(0, 3)
                .map((rec, index) => renderRecommendationCard(rec, index))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Bot className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-sm font-medium text-muted-foreground mb-2">
                暂无推荐
              </h3>
              <p className="text-xs text-muted-foreground">
                等待客户发送消息后生成智能回复建议
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
