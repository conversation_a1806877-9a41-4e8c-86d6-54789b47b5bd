// 全局类型定义
export interface ElectronAPI {
  // 通用IPC通信接口
  invoke: <T = unknown>(channel: string, ...args: unknown[]) => Promise<T>
  on: (channel: string, listener: (...args: unknown[]) => void) => void
  removeAllListeners: (channel: string) => void

  // 窗口控制 API
  window: {
    minimize: () => Promise<void>
    close: () => Promise<void>
    toggle: () => Promise<void>
    isVisible: () => Promise<boolean>
    setAlwaysOnTop: (flag: boolean) => Promise<void>
  }

  // 系统功能 API
  shell: {
    openExternal: (url: string) => Promise<boolean>
  }

  // 应用信息 API
  app: {
    getInfo: () => Promise<{
      name: string
      version: string
      isDev: boolean
    }>
  }

  // 系统信息
  platform: NodeJS.Platform
  versions: {
    node: string
    chrome: string
    electron: string
  }
}

// 应用配置类型
export interface AppConfig {
  apiBaseUrl: string
  websocketUrl: string
  version: string
  isDev: boolean
}

// QianNiu 相关类型
export interface QianNiuConnectionInfo {
  id: string
  clientId: string
  connectionId: string
  isOnline: boolean
  connectedAt: string
  lastActivity: string
  platformType: string
  organizationId: string
  teamId: string
  client: {
    id: string
    name: string
    team: {
      id: string
      name: string
    }
  }
  currentUser?: {
    id: string
    nickname: string
    avatar?: string
    platformCustomerId: string
    conversationId: string
    lastActivity: string
  }
}

// AI 推荐类型
export interface AIRecommendation {
  id: string
  content: string
  confidence: number
  timestamp: string
}

// 消息类型
export interface Message {
  id: string
  conversationId: string
  content: string
  messageType: 'TEXT' | 'IMAGE' | 'FILE'
  senderType: 'CUSTOMER' | 'CUSTOMER_SERVICE'
  sentAt: string
}

// API 响应类型
export interface APIResponse<T = unknown> {
  success: boolean
  data: T
  message: string
}

// 环境变量类型
export interface ImportMetaEnv {
  readonly VITE_API_URL: string
  readonly VITE_WS_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_DEV_SERVER_PORT: string
  readonly DEV: boolean
  readonly PROD: boolean
}
