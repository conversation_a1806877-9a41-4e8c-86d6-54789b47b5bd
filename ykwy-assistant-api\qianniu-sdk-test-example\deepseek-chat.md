以下是完整的千牛智能客服SDK调用示例，包含所有接口的统一调用格式（基于`imsdk.invoke`或`QN.app.invoke`）：

---

### **1. 订单相关接口**

#### 邀请下单

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.airisland.invite.order.send',
  param: {
    encryptId: 'RAzN8BQaCakmNLV4DBQGxZrh2qsaF',
    bizDomain: 'taobao',
    encrypType: 'internal',
    buyerNick: 'tb783904683',
    itemProps: '[{"itemId":************,"skuId":5191249981449,"quantity":1,"context":{}}]',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

#### 查询近三个月订单

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.trade.query',
  param: {
    orderStatus: 'UNPAID_TRADE',
    securityBuyerUid: 'RAzN8BQnBnvyxWLCkdLVbmyc6sVtT',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

#### 查询历史订单

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.trade.history.query',
  param: {
    securityBuyerUid: 'RAzN8BQjwwwxzFPmfGtdD4UdTx2vb',
    pageNum: 1,
    pageSize: 10,
  },
  httpMethod: 'post',
  version: '1.0',
});
```

#### 查询订单物流信息

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.alibaba.fulfillment.printorder.consign.logistics.query',
  param: {
    bizOrderId: '3625329531413960410',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

#### 订单解密

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.tid.decrypt',
  param: {
    bizType: 'qianniu',
    tid: '2562771171011380040',
    queryByTid: true,
  },
  httpMethod: 'post',
  version: '1.0',
});
```

---

### **2. 商品相关接口**

#### 发送商品卡片

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.item.recommend.send',
  param: {
    encryptId: 'RAzN8BQgcJTmZi38Xpj5igvrzDYFz',
    batchItemIds: '[************]',
    type: -1,
  },
  httpMethod: 'post',
  version: '1.0',
});
```

#### 查询商品记录（咨询/购买/足迹）

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.item.record.query',
  param: {
    encryptId: 'RAzN8BQgcJTmZi38Xpj5igvrzDYFz',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

#### 搜索店铺商品

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.item.search',
  param: {
    pageSize: 8,
    pageNo: 1,
    keyWord: '806742484226',
    sortKey: 'sold',
    desc: true,
    type: 0,
    queryGift: false,
    encryptId: 'RAzN8BQuL2aMkkVeK6SGe3CZHv7eb',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

---

### **3. 用户相关接口**

#### 查询客户信息

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.user.query',
  param: {
    encryptId: 'RAzN8BQgcJTmZi38Xpj5igvrzDYFz',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

#### 查询买家ID（通过昵称）

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.airisland.contact.search',
  param: {
    accessKey: 'qianniu-pc',
    accessSecret: 'qianniu-pc-secret',
    accountType: '3',
    searchQuery: '买家昵称',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

---

### **4. 客服操作接口**

#### 挂起会话

```javascript
await QN.app.invoke({
  api: 'invokeMTopChannelService',
  query: {
    method: 'mtop.taobao.qianniu.cloudkefu.suspend.set',
    param: {
      account_id: *************,
      source: 1,
      is_suspend: true,
    },
    httpMethod: 'post',
    version: '1.0',
  },
});
```

#### 转接到个人客服

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.forward',
  param: {
    buyerId: **********,
    toId: ***********,
    reason: '转接',
    options: JSON.stringify({
      appCid: '**********.1-**********.1#11001@cntaobao',
      buyerDomain: 'cntaobao',
      loginDomain: 'cntaobao',
    }),
  },
  httpMethod: 'post',
  version: '3.0',
});
```

#### 获取所有客服分组

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.dispatchgroups.get',
  param: { login_domain: 'cntaobao' },
  httpMethod: 'post',
  version: '2.0',
});
```

#### 转接到客服分组

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.forward',
  param: {
    buyerId: **********,
    toId: 0,
    reason: '转接',
    options: JSON.stringify({
      groupId: 172634466,
      appCid: '**********.1-**********.1#11001@cntaobao',
      forwardType: 2,
      charset: 'utf-8',
      exceptUsers: '',
      buyerDomain: 'cntaobao',
      loginDomain: 'cntaobao',
    }),
  },
  httpMethod: 'post',
  version: '3.0',
});
```

---

### **5. 优惠券相关接口**

#### 查询店铺优惠券

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.user.shop.coupon.query',
  param: {},
  httpMethod: 'post',
  version: '1.0',
});
```

#### 发送优惠券

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cs.user.shop.coupon.send',
  param: {
    name: '店铺优惠卷',
    activityId: 'c837b1e32d4d4ff8aab58d0f3ed68715',
    description: '满10000减1000元',
    encryptId: 'RAzN8BQgcJTmZi38Xpj5igvrzDYFz',
  },
  httpMethod: 'post',
  version: '1.0',
});
```

---

### **6. 客服账号查询**

```javascript
await imsdk.invoke('application.invokeMTopChannelService', {
  method: 'mtop.taobao.qianniu.cloudkefu.accountstatus.getbyid',
  param: {
    pageSize: 100,
  },
  httpMethod: 'post',
  version: '1.0',
});
```

---

### **7. 发送消息相关接口**

#### 发送文本消息到输入框

```javascript
await imsdk.invoke('application.insertText2Inputbox',
  { 
    uid: "cntaobao" + "tb783904683", 
    text: "Hello, World!"
  }
)
```

--- 

### 调用说明：

1. **统一结构**：所有接口均通过 `imsdk.invoke` 或 `QN.app.invoke` 调用。
2. **必填参数**：
   - `method`: MTOP接口名称
   - `param`: 业务参数（JSON格式）
   - `httpMethod`: 固定为 `post`
   - `version`: 接口版本（通常为 `1.0` 或 `3.0`）
3. **特殊处理**：
   - 转接接口的 `options` 参数需要 `JSON.stringify` 处理。
   - 商品/订单ID需替换为实际值。
