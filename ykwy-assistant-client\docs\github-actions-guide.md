# GitHub Actions 自动化构建指南

本文档详细说明了项目中配置的 GitHub Actions 工作流的使用方法、配置选项和故障排除。

## 📋 工作流概览

### 多平台构建发布工作流 (`build-release.yml`)

**用途**：构建多平台版本并自动创建 GitHub Release

**触发条件**：

- 创建版本标签 (`v*`)
- 手动触发，支持选择发布类型

**构建产物**：

- Windows: NSIS 安装包 (`.exe`)
- macOS: DMG 镜像 (`.dmg`) \*可选
- Linux: AppImage (`.AppImage`) \*可选

**发布类型**：

- `draft`: 草稿发布（默认）
- `prerelease`: 预发布版本
- `release`: 正式发布

## 🚀 使用方法

### 版本发布流程

1. **创建版本标签**

   ```bash
   # 确保本地代码是最新的
   git pull origin main

   # 创建并推送版本标签
   git tag v1.0.0
   git push origin v1.0.0
   ```

2. **自动构建**

   - GitHub Actions 会自动检测到新标签
   - 触发 `build-release.yml` 工作流
   - 构建所有平台的安装包

3. **创建 Release**
   - 自动创建 GitHub Release
   - 上传所有构建产物
   - 生成发布说明模板

### 手动触发构建

1. **进入 Actions 页面**

   - 访问 `https://github.com/your-username/your-repo/actions`

2. **选择工作流**

   - 点击要运行的工作流名称

3. **触发构建**
   - 点击 "Run workflow" 按钮
   - 选择分支（如果适用）
   - 填写参数（如果有）
   - 点击绿色 "Run workflow" 按钮

### 开发流程集成

- **日常开发**：推送到 `main` 分支会自动触发 Windows 构建
- **PR 审查**：创建 PR 会自动运行代码检查和构建验证
- **功能开发**：推送到 `feature/*` 分支会运行基础检查

## ⚙️ 高级配置

### 代码签名配置

1. **获取代码签名证书**

   - 从证书颁发机构购买代码签名证书
   - 导出为 `.p12` 或 `.pfx` 格式

2. **配置 GitHub Secrets**

   ```bash
   # 将证书文件转换为 base64
   base64 -i certificate.p12 > certificate.base64
   ```

   在 GitHub 仓库设置中添加：

   - `CSC_LINK`: 证书文件的 base64 编码内容
   - `CSC_KEY_PASSWORD`: 证书密码

3. **启用签名**
   在工作流文件中修改：
   ```yaml
   env:
     CSC_IDENTITY_AUTO_DISCOVERY: true
     CSC_LINK: ${{ secrets.CSC_LINK }}
     CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
   ```

### 自定义构建配置

**修改目标平台**：
编辑 `scripts/electron-builder.json`：

```json
{
  "win": {
    "target": [
      { "target": "nsis", "arch": ["x64"] },
      { "target": "portable", "arch": ["x64"] }
    ]
  }
}
```

**添加构建选项**：

```yaml
- name: 构建应用
  run: pnpm build:win
  env:
    CSC_IDENTITY_AUTO_DISCOVERY: false
    # 启用调试模式
    DEBUG: electron-builder
    # 自定义输出目录
    BUILD_DIR: custom-release
```

### 缓存优化

工作流已配置 pnpm 缓存：

```yaml
- name: 设置 pnpm 缓存
  uses: actions/cache@v4
  with:
    path: ${{ env.STORE_PATH }}
    key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
```

**手动清理缓存**：

1. 进入 Actions 页面
2. 点击左侧 "Caches"
3. 删除旧的缓存条目

## 🔍 故障排除

### 常见错误及解决方案

#### 1. 依赖安装失败

```
Error: Cannot resolve dependency '@types/node'
```

**解决方案**：

- 检查 `pnpm-lock.yaml` 是否已提交
- 确保 Node.js 版本兼容
- 清理缓存并重新安装

#### 2. 构建失败

```
Error: Build failed with exit code 1
```

**解决方案**：

- 检查本地构建是否正常：`pnpm build`
- 查看详细错误日志
- 确保所有必要文件已提交

#### 3. 代码签名失败

```
Error: Code signing failed
```

**解决方案**：

- 验证证书格式和密码
- 检查证书是否过期
- 暂时禁用签名：`CSC_IDENTITY_AUTO_DISCOVERY: false`

#### 4. 工作流权限错误

```
Error: Resource not accessible by integration
```

**解决方案**：

- 确保 `GITHUB_TOKEN` 有足够权限
- 检查仓库的 Actions 权限设置
- 验证工作流文件语法

### 调试技巧

1. **启用调试日志**

   ```yaml
   env:
     ACTIONS_STEP_DEBUG: true
     ACTIONS_RUNNER_DEBUG: true
   ```

2. **使用 tmate 进行远程调试**

   ```yaml
   - name: 调试会话
     if: failure()
     uses: mxschmitt/action-tmate@v3
   ```

3. **保存调试产物**
   ```yaml
   - name: 上传调试信息
     if: failure()
     uses: actions/upload-artifact@v4
     with:
       name: debug-logs
       path: |
         *.log
         dist/
   ```

## 📊 监控和通知

### 构建状态徽章

在 README.md 中添加构建状态：

```markdown
![Build Status](https://github.com/your-username/your-repo/workflows/构建%20Windows%20应用包/badge.svg)
```

### 通知配置

**Slack 通知**：

```yaml
- name: Slack 通知
  if: always()
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

**邮件通知**：
GitHub 默认会向提交者和仓库 watch 者发送失败通知。

## 🔄 版本管理策略

### 语义化版本控制

建议使用语义化版本号：

- `v1.0.0`: 主版本（破坏性更改）
- `v1.1.0`: 次版本（新功能）
- `v1.1.1`: 修订版本（错误修复）

### 预发布版本

```bash
# 创建预发布版本
git tag v1.1.0-beta.1
git push origin v1.1.0-beta.1
```

工作流会自动识别并标记为预发布版本。

### 自动版本管理

可以集成自动版本管理工具：

- [semantic-release](https://github.com/semantic-release/semantic-release)
- [release-please](https://github.com/googleapis/release-please)

## 📚 相关资源

- [GitHub Actions 官方文档](https://docs.github.com/en/actions)
- [electron-builder 文档](https://www.electron.build/)
- [pnpm 文档](https://pnpm.io/)
- [代码签名指南](https://www.electron.build/code-signing)

## 🤝 贡献指南

如需修改工作流配置：

1. Fork 仓库
2. 创建功能分支：`git checkout -b feature/workflow-improvement`
3. 修改工作流文件
4. 测试配置：创建 PR 触发检查工作流
5. 提交 PR 并描述更改内容

---

如有问题或建议，请创建 Issue 或联系项目维护者。
