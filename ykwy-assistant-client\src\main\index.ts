import {
  app,
  BrowserWindow,
  ipcMain,
  globalShortcut,
  shell,
  screen
} from 'electron'
import { join } from 'path'
import { existsSync } from 'fs'
import { isDev } from './utils'

// 设置环境变量处理
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true'

class AssistantClient {
  private mainWindow: BrowserWindow | null = null
  private isWindowVisible = false

  constructor() {
    this.init()
  }

  private async init() {
    // 防止多实例运行
    if (!app.requestSingleInstanceLock()) {
      app.quit()
      return
    }

    // 应用准备就绪后初始化
    if (app.isReady()) {
      await this.onReady()
    } else {
      app.whenReady().then(() => this.onReady())
    }

    // 注册事件监听器
    this.registerEventListeners()
    this.registerIpcHandlers()
  }

  private async onReady() {
    await this.createMainWindow()
    this.registerGlobalShortcuts()
  }

  private registerEventListeners() {
    // 当所有窗口都被关闭时
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })

    // macOS 应用激活时
    app.on('activate', async () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        await this.createMainWindow()
      }
    })

    // 应用即将退出时
    app.on('will-quit', () => {
      globalShortcut.unregisterAll()
    })

    // 第二个实例启动时
    app.on('second-instance', () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) {
          this.mainWindow.restore()
        }
        this.mainWindow.focus()
      }
    })
  }

  private registerIpcHandlers() {
    // 窗口控制 IPC 处理程序
    ipcMain.handle('window-minimize', () => {
      this.mainWindow?.minimize()
    })

    ipcMain.handle('window-close', () => {
      this.mainWindow?.close()
    })

    ipcMain.handle('window-toggle', () => {
      this.toggleWindow()
    })

    ipcMain.handle('window-is-visible', () => {
      return this.isWindowVisible && this.mainWindow?.isVisible()
    })

    ipcMain.handle('window-set-always-on-top', (_event, flag: boolean) => {
      this.mainWindow?.setAlwaysOnTop(flag)
    })

    // 系统功能 IPC 处理程序
    ipcMain.handle('shell:openExternal', async (_event, url: string) => {
      try {
        await shell.openExternal(url)
        return true
      } catch (error) {
        console.error('打开外部链接失败:', error)
        return false
      }
    })

    // 应用信息 IPC 处理程序
    ipcMain.handle('app:getInfo', async () => {
      return {
        name: app.getName(),
        version: app.getVersion(),
        isDev: isDev
      }
    })

    // 环境变量获取 IPC 处理程序
    ipcMain.handle('get-env-var', (_event, key: string) => {
      return process.env[key] || ''
    })
  }

  private async createMainWindow() {
    const { height: screenHeight } = screen.getPrimaryDisplay().workAreaSize

    // 窗口配置 - 无标题栏设计
    const windowConfig = {
      width: parseInt(process.env.VITE_WINDOW_WIDTH || '400'), // 减少默认宽度
      height: parseInt(process.env.VITE_WINDOW_HEIGHT || '600'),
      minWidth: parseInt(process.env.VITE_WINDOW_MIN_WIDTH || '350'),
      minHeight: parseInt(process.env.VITE_WINDOW_MIN_HEIGHT || '500'),
      maxWidth: parseInt(process.env.VITE_WINDOW_MAX_WIDTH || '600'),
      maxHeight: screenHeight,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, 'preload.js'),
        webSecurity: !isDev
      },
      show: true,
      frame: false, // 移除窗口框架和标题栏
      titleBarStyle: 'hidden' as const, // 隐藏标题栏
      autoHideMenuBar: true, // 隐藏菜单栏
      resizable: true, // 保持可调整大小
      alwaysOnTop: true, // 默认置顶
      skipTaskbar: false, // 在任务栏显示
      icon: join(__dirname, '../../public/icon.ico')
    }

    this.mainWindow = new BrowserWindow(windowConfig)

    // 加载应用
    if (isDev) {
      // 固定使用9000端口，与渲染进程开发服务器保持一致
      const port = 5174
      const url = `http://localhost:${port}`

      console.log(`🚀 [Main] 开发模式: 加载 ${url}`)
      this.mainWindow.webContents.openDevTools()
      await this.mainWindow.loadURL(url)
    } else {
      const htmlPath = join(__dirname, '../renderer/index.html')
      console.log(`生产模式: 加载 ${htmlPath}`)

      if (existsSync(htmlPath)) {
        await this.mainWindow.loadFile(htmlPath)
      } else {
        console.error('找不到渲染进程文件:', htmlPath)
      }
    }

    // 窗口事件处理
    this.mainWindow.once('ready-to-show', () => {
      if (!this.mainWindow) return

      this.mainWindow.show()
      this.isWindowVisible = true

      if (isDev) {
        this.mainWindow.webContents.openDevTools()
      }
    })

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
      this.isWindowVisible = false
    })

    this.mainWindow.on('hide', () => {
      this.isWindowVisible = false
    })

    this.mainWindow.on('show', () => {
      this.isWindowVisible = true
    })
  }

  private registerGlobalShortcuts() {
    // 注册全局快捷键: Ctrl+Shift+A (Windows/Linux) 或 Cmd+Shift+A (macOS)
    const toggleShortcut =
      process.platform === 'darwin' ? 'Cmd+Shift+A' : 'Ctrl+Shift+A'

    const ret = globalShortcut.register(toggleShortcut, () => {
      this.toggleWindow()
    })

    if (!ret) {
      console.log('全局快捷键注册失败')
    } else {
      console.log(`全局快捷键已注册: ${toggleShortcut}`)
    }
  }

  private toggleWindow() {
    if (!this.mainWindow) return

    if (this.mainWindow.isVisible() && this.isWindowVisible) {
      this.hideWindow()
    } else {
      this.showWindow()
    }
  }

  private showWindow() {
    if (!this.mainWindow) return

    this.mainWindow.show()
    this.mainWindow.focus()
    this.isWindowVisible = true
  }

  private hideWindow() {
    if (!this.mainWindow) return

    this.mainWindow.hide()
    this.isWindowVisible = false
  }
}

// 错误处理
process.on('uncaughtException', error => {
  console.error('Uncaught Exception:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// 启动应用
new AssistantClient()
