import { useQuery } from '@tanstack/react-query'
import { apiClient } from '@/lib/api-client'
import { normalizeConversationCode } from '@/lib/utils'

// TCP连接信息类型
interface TcpConnection {
  id: string
  localAddress: string
  localPort: number
  remoteAddress: string
  remotePort: number
  state: string
  [key: string]: unknown
}

// 连接映射类型
interface ConnectionMapping {
  tcpConnectionId: string
  clientId: string
  connectionId: string
  currentConversationId?: string
  [key: string]: unknown
}

// 在线客户端类型
interface OnlineClient {
  id: string
  name: string
  connectionId: string
  accounts: Array<{
    accountName: string
    shopName: string
    platformType: string
    conversations: Array<{
      id: string
      title: string
      lastActivity: string
      customer: {
        nickname: string
        avatar?: string
      }
    }>
  }>
}

// 千牛客户端状态类型
export interface QianNiuStatus {
  isConnected: boolean
  currentConversationId: string | null
  activeClients: number
  lastActivity: string
  connectionInfo?: {
    tcpConnections: TcpConnection[]
    connectionMappings: Record<string, ConnectionMapping>
    totalConnections: number
    onlineClients?: OnlineClient[]
    unmappedTcpConnections?: TcpConnection[]
  }
  platformConnections?: Array<{
    id: string
    clientId: string
    connectionId: string
    isOnline: boolean
    connectedAt: string
    lastActivity: string
    platformType: string
    organizationId: string
    teamId: string
    client?: {
      id: string
      name: string
      team?: {
        id: string
        name: string
      }
    }
    currentUser?: {
      id: string
      nickname: string
      avatar?: string
      platformCustomerId: string
      conversationId: string
      lastActivity: string
    }
  }>
  refetch?: () => void
}

// 监控千牛客户端状态的 hook
export const useQianNiuMonitor = (): QianNiuStatus => {
  console.log('🚀 [useQianNiuMonitor] Hook开始执行')

  console.log('🚀 [useQianNiuMonitor] State初始化完成')

  // 获取连接状态的查询
  const {
    data: connectionData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['qianniu-connection-status'],
    queryFn: async () => {
      try {
        console.log('🔍 [千牛监控] 获取连接状态...')

        // 获取活跃的平台连接
        const response = await apiClient.get<{
          success: boolean
          data: Array<{
            id: string
            clientId: string
            connectionId: string
            isOnline: boolean
            connectedAt: string
            lastActivity: string
            platformType: string
            organizationId: string
            teamId: string
            client?: {
              id: string
              name: string
              team?: {
                id: string
                name: string
              }
            }
            currentUser?: {
              id: string
              nickname: string
              avatar?: string
              platformCustomerId: string
              conversationId: string
              lastActivity: string
            }
          }>
          message: string
        }>('/platform-ws/connections')

        console.log('🔍 [千牛监控] 连接响应:', response)
        console.log('🔍 [千牛监控] response.data:', response.data)
        console.log('🔍 [千牛监控] response.data类型:', typeof response.data)
        const connections = response.data || []
        console.log('🔍 [千牛监控] connections变量:', connections)
        console.log('🔍 [千牛监控] connections类型:', typeof connections)

        // 获取TCP连接状态
        console.log('🔍 [千牛监控] 开始获取TCP连接状态...')
        const tcpResponse = await apiClient.get<{
          data: {
            tcpConnections: TcpConnection[]
            connectionMappings: Record<string, ConnectionMapping>
            totalConnections: number
            onlineClients: OnlineClient[]
            unmappedTcpConnections: TcpConnection[]
          }
        }>('/qianniu-tcp/connections')

        console.log('🔍 [千牛监控] TCP连接响应:', tcpResponse)

        // 详细分析数据
        console.log('🔍 [千牛监控] 详细数据分析:')
        console.log('  - 原始连接数据:', connections)
        console.log('  - 连接数组长度:', connections.length)

        const activeConnections = connections.filter(conn => {
          console.log(
            `  - 检查连接 ${conn.id}: isOnline=${conn.isOnline}, platformType=${conn.platformType}`
          )
          return conn.isOnline && conn.platformType === 'QIANNIU'
        })

        console.log('🔍 [千牛监控] 过滤后的活跃连接:', activeConnections)
        console.log(
          '🔍 [千牛监控] TCP总连接数:',
          tcpResponse.data.totalConnections
        )

        const condition1 = activeConnections.length > 0
        const condition2 = tcpResponse.data.totalConnections > 0
        const isConnected = condition1 || condition2

        console.log('🔍 [千牛监控] 连接状态判断:')
        console.log(
          `  - 条件1 (活跃连接数 > 0): ${condition1} (${activeConnections.length})`
        )
        console.log(
          `  - 条件2 (TCP连接数 > 0): ${condition2} (${tcpResponse.data.totalConnections})`
        )
        console.log(`  - 最终连接状态: ${isConnected}`)

        // 从TCP连接映射中获取当前活跃会话
        const connectionMappings = tcpResponse.data.connectionMappings || {}

        // 从增强的connectionMappings中获取真实的conversationId
        let activeConversationId = null
        const firstConnection = Object.values(
          connectionMappings
        )[0] as ConnectionMapping
        if (firstConnection?.currentConversationId) {
          activeConversationId = firstConnection.currentConversationId
          console.log(
            '🎯 [千牛监控] 使用连接映射中的conversationId:',
            activeConversationId
          )
        } else {
          // 如果连接映射中没有 currentConversationId，设置为 null
          // 不要使用 connectionId，因为那不是真正的 conversationCode
          activeConversationId = null
          console.log('🔄 [千牛监控] 连接映射中没有conversationId，设置为null')
        }

        console.log('🔍 [千牛监控] 构建结果前 - connections:', connections)
        console.log(
          '🔍 [千牛监控] 构建结果前 - connections类型:',
          typeof connections
        )
        console.log(
          '🔍 [千牛监控] 构建结果前 - connections长度:',
          connections?.length
        )

        const result = {
          isConnected,
          currentConversationId: activeConversationId,
          activeClients: activeConnections.length,
          lastActivity:
            activeConnections[0]?.lastActivity || new Date().toISOString(),
          connectionInfo: tcpResponse.data,
          platformConnections: connections, // 这里就是 /platform-ws/connections 返回的 data 数组
          organizationId:
            activeConnections[0]?.organizationId ||
            '01984f1a-eaa4-74c2-88a4-82ff40abaea9'
        }

        console.log('🔍 [千牛监控] 解析结果:', result)
        console.log(
          '🔍 [千牛监控] 结果中的platformConnections:',
          result.platformConnections
        )
        console.log('🔍 [千牛监控] 准备返回结果，类型:', typeof result)

        return result
      } catch (error) {
        console.error('❌ [千牛监控] 获取连接状态失败:', error)
        // 返回默认状态而不是抛出错误
        const fallbackResult = {
          isConnected: false,
          currentConversationId: null,
          activeClients: 0,
          lastActivity: new Date().toISOString(),
          connectionInfo: {
            tcpConnections: [],
            connectionMappings: {},
            totalConnections: 0,
            onlineClients: [],
            unmappedTcpConnections: []
          },
          platformConnections: []
        }
        console.log('🔍 [千牛监控] 返回fallback结果:', fallbackResult)
        return fallbackResult
      }
    },
    staleTime: 2000, // 2秒
    gcTime: 10000, // 10秒
    refetchInterval: 5000, // 5秒轮询
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    retry: 1
  })

  // 添加调试信息
  console.log('🔍 [useQianNiuMonitor] Hook状态调试:', {
    isLoading,
    error: error?.message,
    connectionData: connectionData,
    hasConnectionData: !!connectionData
  })

  // 如果正在加载，返回加载状态
  if (isLoading) {
    return {
      isConnected: false,
      currentConversationId: null,
      activeClients: 0,
      lastActivity: new Date().toISOString(),
      refetch
    }
  }

  // 使用API获取的会话ID，并进行标准化
  const rawConversationId = connectionData?.currentConversationId || null
  const finalConversationId = normalizeConversationCode(rawConversationId)

  console.log('🔄 [千牛监控] 最终conversationId标准化:', {
    原始API: connectionData?.currentConversationId,
    标准化结果: finalConversationId
  })

  const finalResult = {
    isConnected: connectionData?.isConnected || false,
    currentConversationId: finalConversationId,
    activeClients: connectionData?.activeClients || 0,
    lastActivity: connectionData?.lastActivity || new Date().toISOString(),
    connectionInfo: connectionData?.connectionInfo,
    refetch
  }

  console.log('🚀 [useQianNiuMonitor] 最终返回结果:', finalResult)
  console.log('🚀 [useQianNiuMonitor] connectionData详情:', connectionData)

  return finalResult
}
