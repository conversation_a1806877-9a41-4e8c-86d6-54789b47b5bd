// Electron 相关类型扩展
declare global {
  interface Window {
    electronAPI?: {
      invoke: (channel: string, ...args: unknown[]) => Promise<unknown>
      send: (channel: string, ...args: unknown[]) => void
      on: (channel: string, listener: (...args: unknown[]) => void) => void
      removeAllListeners: (channel: string) => void
      window?: {
        isVisible: () => Promise<boolean>
        minimize: () => Promise<void>
        close: () => Promise<void>
        setAlwaysOnTop: (flag: boolean) => Promise<void>
      }
    }
  }

  interface ImportMetaEnv {
    readonly VITE_API_BASE_URL: string
    readonly VITE_WS_URL: string
    readonly VITE_APP_VERSION: string
    readonly DEV: boolean
    readonly PROD: boolean
    readonly [key: string]: unknown
  }

  interface ImportMeta {
    readonly env: ImportMetaEnv
  }
}

// 扩展 CSS 属性以支持 Electron 的 webkitAppRegion
declare module 'react' {
  interface CSSProperties {
    webkitAppRegion?: 'drag' | 'no-drag'
  }
}

export {}
