import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * 合并和去重 Tailwind CSS 类名
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化时间显示
 */
export function formatTime(timestamp: string) {
  const now = new Date()
  const time = new Date(timestamp)
  const diffInMinutes = Math.floor(
    (now.getTime() - time.getTime()) / (1000 * 60)
  )

  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  } else if (diffInMinutes < 24 * 60) {
    return `${Math.floor(diffInMinutes / 60)}小时前`
  } else {
    return `${Math.floor(diffInMinutes / (24 * 60))}天前`
  }
}

/**
 * 格式化金额显示
 */
export function formatMoney(amount: number) {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return function executedFunction(this: unknown, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 标准化 conversationCode
 *
 * 处理各种可能的格式：
 * - "12345" -> "12345"
 * - "12345#suffix" -> "12345"
 * - "12345#user#timestamp" -> "12345"
 *
 * @param conversationId 原始的会话ID或conversationCode
 * @returns 标准化的conversationCode
 */
export function normalizeConversationCode(
  conversationId: string | null | undefined
): string | null {
  if (!conversationId) return null

  // 移除所有可能的后缀，只保留主要的conversationCode部分
  const normalized = conversationId.split('#')[0].trim()

  // 验证是否为有效的conversationCode（简单验证：非空且不包含特殊字符）
  if (!normalized || normalized.length === 0) {
    console.warn(
      '⚠️ [ConversationCode] 无效的conversationCode格式:',
      conversationId
    )
    return null
  }

  console.log('🔄 [ConversationCode] 标准化:', {
    原始: conversationId,
    标准化: normalized,
    是否变化: conversationId !== normalized
  })

  return normalized
}

/**
 * 批量标准化conversationCode数组
 */
export function normalizeConversationCodes(
  conversationIds: (string | null | undefined)[]
): string[] {
  return conversationIds
    .map(id => normalizeConversationCode(id))
    .filter((id): id is string => id !== null)
}

/**
 * 检查两个conversationCode是否表示同一个对话
 */
export function isSameConversation(
  id1: string | null | undefined,
  id2: string | null | undefined
): boolean {
  const normalized1 = normalizeConversationCode(id1)
  const normalized2 = normalizeConversationCode(id2)

  if (!normalized1 || !normalized2) return false

  return normalized1 === normalized2
}
