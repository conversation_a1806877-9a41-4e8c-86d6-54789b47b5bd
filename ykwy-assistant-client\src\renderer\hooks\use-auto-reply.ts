import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api-client'

// 自动回复状态类型
export interface AutoReplyStatus {
  conversationId: string
  autoReplyEnabled: boolean
}

// 设置自动回复状态的请求类型
export interface SetAutoReplyRequest {
  enabled: boolean
}

// 设置自动回复状态的响应类型
export interface SetAutoReplyResponse {
  conversationId: string
  autoReplyEnabled: boolean
  changed: boolean
}

// 获取自动回复状态的Hook
export function useAutoReplyStatus(conversationId: string | null) {
  return useQuery({
    queryKey: ['autoReplyStatus', conversationId],
    queryFn: async () => {
      if (!conversationId) {
        throw new Error('对话ID不能为空')
      }

      console.log('🔍 [AutoReply] 获取自动回复状态:', conversationId)

      const response = await apiClient.get<{
        success: boolean
        data: AutoReplyStatus
        message: string
      }>(`/conversation-auto-reply/${conversationId}/auto-reply`)

      console.log('✅ [AutoReply] 获取状态成功:', response.data)
      return response.data
    },
    enabled: !!conversationId,
    staleTime: 30000, // 30秒内不重新获取
    retry: 2
  })
}

// 设置自动回复状态的Hook
export function useSetAutoReply() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      conversationId,
      enabled
    }: {
      conversationId: string
      enabled: boolean
    }) => {
      console.log('🔄 [AutoReply] 设置自动回复状态:', {
        conversationId,
        enabled
      })

      const response = await apiClient.put<{
        success: boolean
        data: SetAutoReplyResponse
        message: string
      }>(`/conversation-auto-reply/${conversationId}/auto-reply`, { enabled })

      console.log('✅ [AutoReply] 设置状态成功:', response.data)
      return response.data
    },
    onSuccess: (data, variables) => {
      // 更新缓存
      queryClient.setQueryData(['autoReplyStatus', variables.conversationId], {
        conversationId: data.conversationId,
        autoReplyEnabled: data.autoReplyEnabled
      })

      // 如果状态发生了变化，显示提示
      if (data.changed) {
        console.log(
          `🎉 [AutoReply] 自动回复已${data.autoReplyEnabled ? '开启' : '关闭'}`
        )
      }
    },
    onError: error => {
      console.error('❌ [AutoReply] 设置状态失败:', error)
    }
  })
}

// 综合的自动回复管理Hook
export function useAutoReply(conversationId: string | null) {
  const [isToggling, setIsToggling] = useState(false)

  // 获取当前状态
  const {
    data: status,
    isLoading,
    error,
    refetch
  } = useAutoReplyStatus(conversationId)

  // 设置状态的mutation
  const setAutoReplyMutation = useSetAutoReply()

  // 切换自动回复状态
  const toggleAutoReply = async () => {
    if (!conversationId || !status || isToggling) {
      return
    }

    setIsToggling(true)
    try {
      await setAutoReplyMutation.mutateAsync({
        conversationId,
        enabled: !status.autoReplyEnabled
      })
    } catch (error) {
      console.error('❌ [AutoReply] 切换失败:', error)
    } finally {
      setIsToggling(false)
    }
  }

  // 设置指定的自动回复状态
  const setAutoReply = async (enabled: boolean) => {
    if (!conversationId || isToggling) {
      return
    }

    setIsToggling(true)
    try {
      await setAutoReplyMutation.mutateAsync({
        conversationId,
        enabled
      })
    } catch (error) {
      console.error('❌ [AutoReply] 设置失败:', error)
    } finally {
      setIsToggling(false)
    }
  }

  return {
    // 状态
    autoReplyEnabled: status?.autoReplyEnabled ?? false,
    isLoading: isLoading || isToggling,
    error,

    // 操作
    toggleAutoReply,
    setAutoReply,
    refetch,

    // 内部状态
    isToggling,
    mutation: setAutoReplyMutation
  }
}
