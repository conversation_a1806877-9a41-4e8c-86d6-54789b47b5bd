import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import type { ElectronAPI } from '@shared/types'

// 安全地暴露 Electron API 到渲染进程
const electronAPI: ElectronAPI = {
  // 通用IPC通信接口
  invoke: <T = unknown>(channel: string, ...args: unknown[]): Promise<T> => {
    return ipcRenderer.invoke(channel, ...args)
  },

  on: (channel: string, listener: (...args: unknown[]) => void) => {
    ipcRenderer.on(channel, listener)
  },

  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel)
  },

  // 窗口控制 API
  window: {
    minimize: () => ipcRenderer.invoke('window-minimize'),
    close: () => ipcRenderer.invoke('window-close'),
    toggle: () => ipcRenderer.invoke('window-toggle'),
    isVisible: () => ipcRenderer.invoke('window-is-visible'),
    setAlwaysOnTop: (flag: boolean) =>
      ipcRenderer.invoke('window-set-always-on-top', flag)
  },

  // 系统功能 API
  shell: {
    openExternal: (url: string) => ipcRenderer.invoke('shell:openExternal', url)
  },

  // 应用信息 API
  app: {
    getInfo: () => ipcRenderer.invoke('app:getInfo')
  },

  // 系统信息
  platform: process.platform,
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  }
}

// 将 API 暴露到全局 window 对象
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// 开发环境下的调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 预加载脚本已加载 - 完整功能版本')
  console.log('📦 可用的 API:', Object.keys(electronAPI))
}
