import { useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api-client'
import { normalizeConversationCode } from '@/lib/utils'

// 发送消息请求数据类型
export interface SendMessageData {
  conversationId: string
  content: string
  messageType?: 'TEXT' | 'IMAGE' | 'FILE'
  parentMessageId?: string
}

// 消息响应类型
interface Message {
  id: string
  content: string
  messageType: string
  senderId: string
  conversationId: string
  createdAt: string
  updatedAt: string
}

interface ApiResponse<T> {
  data: T
  message?: string
  success?: boolean
}

// 发送消息 mutation
export const useSendMessage = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: SendMessageData): Promise<Message> => {
      console.log('🚀 [桌面端] 发送消息:', data)

      // 使用统一的标准化函数处理conversationCode
      const conversationCode = normalizeConversationCode(data.conversationId)

      if (!conversationCode) {
        throw new Error('无效的conversationId格式')
      }

      console.log('🔍 [桌面端] 原始conversationId:', data.conversationId)
      console.log('🔍 [桌面端] 标准化conversationCode:', conversationCode)

      const response = await apiClient.post<ApiResponse<Message>>(
        `conversations/${encodeURIComponent(conversationCode)}/messages`,
        {
          content: data.content,
          parentMessageId: data.parentMessageId,
          messageType: data.messageType || 'TEXT'
        }
      )

      console.log('✅ [桌面端] 消息发送成功:', response)
      return response.data
    },
    onSuccess: (_data, variables) => {
      console.log('✅ [桌面端] 消息发送成功，准备刷新AI推荐')

      const conversationCode = normalizeConversationCode(
        variables.conversationId
      )

      if (!conversationCode) {
        console.error('❌ [桌面端] 无法提取conversationCode，跳过缓存更新')
        return
      }

      // 直接执行缓存更新，移除延迟避免竞态条件
      try {
        // 简单地让AI推荐缓存过期，让其自然重新获取
        queryClient.invalidateQueries({
          queryKey: ['aiRecommendations', conversationCode],
          exact: true
        })

        console.log('🔄 [桌面端] AI推荐缓存已标记为过期，将重新获取')
      } catch (cacheError) {
        console.error('❌ [桌面端] 缓存操作失败:', cacheError)
      }
    },
    onError: (error, variables) => {
      console.error('❌ [桌面端] 消息发送失败:', error, variables)
    }
  })
}
