import { Hono } from 'hono';

import { connectionTracker } from '../lib/connection-tracker';
import { prisma } from '../lib/db';
import { logger } from '../lib/logger';

const app = new Hono();

/**
 * 生成千牛注入脚本 - 完整功能版本
 */
app.get('/qianniu-inject/:invitationId.js', async (c) => {
  const requestStartTime = Date.now();
  const invitationId = c.req.param('invitationId');
  const userAgent = c.req.header('user-agent') || 'unknown';
  const clientIP = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';

  logger.info('收到脚本注入请求', {
    invitationId,
    userAgent,
    clientIP,
    requestTime: new Date().toISOString(),
    endpoint: `/qianniu-inject/${invitationId}.js`,
    method: 'GET',
  });

  try {
    // 查找邀请
    logger.debug('查询连接邀请信息', {
      invitationId,
      queryStartTime: new Date().toISOString(),
    });

    const dbQueryStartTime = Date.now();
    const invitation = await prisma.connectionInvitation.findUnique({
      where: { id: invitationId },
      include: {
        organization: {
          select: { id: true, name: true },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });
    const dbQueryDuration = Date.now() - dbQueryStartTime;

    logger.debug('数据库查询完成', {
      invitationId,
      queryDuration: `${dbQueryDuration}ms`,
      found: !!invitation,
      organizationId: invitation?.organizationId,
      teamId: invitation?.teamId,
    });

    // 验证邀请
    if (!invitation) {
      logger.warn('脚本请求失败：邀请不存在', {
        invitationId,
        clientIP,
        userAgent,
        errorType: 'invitation_not_found',
        responseCode: 404,
      });
      return c.text('// 邀请不存在', 404, {
        'Content-Type': 'application/javascript',
      });
    }

    logger.info('找到连接邀请', {
      invitationId,
      organizationId: invitation.organizationId,
      organizationName: invitation.organization.name,
      teamId: invitation.teamId,
      teamName: invitation.team.name,
      invitationName: invitation.name,
      status: invitation.status,
      expiresAt: invitation.expiresAt.toISOString(),
      createdAt: invitation.createdAt.toISOString(),
    });

    // 允许 PENDING 和 ACTIVATED 状态的邀请（支持脚本更新）
    if (invitation.status !== 'PENDING' && invitation.status !== 'ACTIVATED') {
      logger.warn('脚本请求失败：邀请状态无效', {
        invitationId,
        currentStatus: invitation.status,
        allowedStatuses: ['PENDING', 'ACTIVATED'],
        clientIP,
        userAgent,
        errorType: 'invalid_status',
        responseCode: 410,
      });
      return c.text('// 邀请已失效或已撤销', 410, {
        'Content-Type': 'application/javascript',
      });
    }

    const now = new Date();
    if (invitation.expiresAt < now) {
      logger.warn('脚本请求失败：邀请已过期', {
        invitationId,
        expiresAt: invitation.expiresAt.toISOString(),
        currentTime: now.toISOString(),
        expiredFor: `${Math.round((now.getTime() - invitation.expiresAt.getTime()) / (1000 * 60 * 60))} hours`,
        clientIP,
        userAgent,
        errorType: 'invitation_expired',
        responseCode: 410,
      });

      // 自动标记为过期
      try {
        await prisma.connectionInvitation.update({
          where: { id: invitationId },
          data: { status: 'EXPIRED' },
        });
        logger.debug('邀请状态已更新为过期', { invitationId });
      } catch (updateError) {
        logger.error('更新邀请过期状态失败', { invitationId }, updateError instanceof Error ? updateError : new Error(String(updateError)));
      }

      return c.text('// 邀请已过期', 410, {
        'Content-Type': 'application/javascript',
      });
    }

    // 记录脚本生成追踪
    logger.debug('记录脚本生成追踪', {
      invitationId,
      organizationId: invitation.organizationId,
      teamId: invitation.teamId,
      clientIP,
      userAgent,
    });

    connectionTracker.trackScriptGeneration(invitation.id, invitation.organizationId, invitation.teamId, {
      userAgent: c.req.header('user-agent'),
      ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      organizationName: invitation.organization.name,
      teamName: invitation.team.name,
      invitationName: invitation.name,
    });

    // 生成完整功能的注入脚本
    logger.info('开始生成注入脚本', {
      invitationId,
      organizationId: invitation.organizationId,
      teamId: invitation.teamId,
      scriptType: 'qianniu_injection',
    });

    const scriptGenerationStartTime = Date.now();
    const injectionScript = generateInjectionScript(invitation);
    const scriptGenerationDuration = Date.now() - scriptGenerationStartTime;
    const totalRequestDuration = Date.now() - requestStartTime;

    logger.info('脚本生成成功，返回给客户端', {
      invitationId,
      scriptLength: injectionScript.length,
      scriptLengthKB: Math.round((injectionScript.length / 1024) * 100) / 100,
      scriptGenerationDuration: `${scriptGenerationDuration}ms`,
      totalRequestDuration: `${totalRequestDuration}ms`,
      clientIP,
      userAgent,
      responseCode: 200,
      cacheControl: 'no-cache',
      deliveryMethod: 'direct_api',
    });

    return c.text(injectionScript, 200, {
      'Content-Type': 'application/javascript',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
    });
  } catch (error) {
    const totalRequestDuration = Date.now() - requestStartTime;
    const errorDetails = {
      invitationId,
      clientIP,
      userAgent,
      totalRequestDuration: `${totalRequestDuration}ms`,
      errorType: error instanceof Error ? error.constructor.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined,
      endpoint: `/qianniu-inject/${invitationId}.js`,
      responseCode: 500,
      impact: '客户端无法获取注入脚本，千牛连接功能不可用',
      troubleshooting: {
        checkDatabase: '检查数据库连接和邀请记录',
        checkScriptGeneration: '检查脚本生成逻辑',
        checkEnvironmentVars: '检查环境变量配置',
        checkNetworkConnectivity: '检查网络连接',
      },
    };

    logger.error('CDN注入脚本生成失败', errorDetails, error instanceof Error ? error : new Error(String(error)));

    return c.text('// 脚本生成失败，请联系技术支持', 500, {
      'Content-Type': 'application/javascript',
    });
  }
});

export function generateInjectionScript(invitation: {
  id: string;
  organizationId: string;
  teamId: string;
  name: string;
  organization: { id: string; name: string };
  team: { id: string; name: string };
}): string {
  const isDevelopment = Bun.env.NODE_ENV === 'development';

  let wsUrl: string;
  if (isDevelopment) {
    // 开发环境使用本地连接
    const apiHost = 'localhost';
    const apiPort = Bun.env['PORT'] || '3002';
    wsUrl = `ws://${apiHost}:${apiPort}/platform-ws/qianniu?invitationId=${invitation.id}`;
  } else {
    // 非开发环境使用环境变量中的API URL
    const apiUrl = Bun.env['YKWY_ASSISTANT_API_URL'] || 'https://ykwy-assistant-api.wuyoutansuo.com';
    // 确保 URL 末尾没有斜杠，然后替换协议
    const cleanApiUrl = apiUrl.replace(/\/$/, '').replace('https://', 'wss://');
    wsUrl = `${cleanApiUrl}/platform-ws/qianniu?invitationId=${invitation.id}`;
  }

  return `/**
 * 千牛客户端注入脚本 (支持脚本更新)
 * 连接ID: ${invitation.id}
 * 组织: ${invitation.organization.name}
 * 团队: ${invitation.team.name}
 * 生成时间: ${new Date().toISOString()}
 */

// 加载原始千牛脚本，保持千牛原有功能
const script = document.createElement("script");
script.type = "text/javascript";
script.src = "https://iseiya.taobao.com/imsupport";
document.getElementsByTagName("body")[0].appendChild(script);

// 防止重复注入
if (typeof window.___setupWebSocket === 'undefined') {
  // 初始化买家缓存
  window._buyerCache = new Map();

  // 连接配置
  window.QIANNIU_CONFIG = {
    invitationId: '${invitation.id}',
    organizationId: '${invitation.organizationId}',
    teamId: '${invitation.teamId}',
    name: '${invitation.name}',
    organization: {
      id: '${invitation.organization.id}',
      name: '${invitation.organization.name}'
    },
    team: {
      id: '${invitation.team.id}',
      name: '${invitation.team.name}'
    },
    websocketUrl: '${wsUrl}'
  };

  // WebSocket连接管理
  window.___setupWebSocket = function() {
    if (window.chatWebsocket && window.chatWebsocket.readyState === WebSocket.OPEN) {
      return; // 如果已有活跃连接，不再创建新连接
    }

    let heartbeatInterval;
    let socket = new WebSocket(window.QIANNIU_CONFIG.websocketUrl);

    socket.onopen = async (e) => {
      console.log('[千牛WebSocket] 连接成功');

      // 发送连接激活信息
      const activationMessage = {
        type: 'qianniu_connect',
        invitationId: window.QIANNIU_CONFIG.invitationId,
        organizationId: window.QIANNIU_CONFIG.organizationId,
        teamId: window.QIANNIU_CONFIG.teamId,
        clientInfo: {
          name: window.QIANNIU_CONFIG.name,
          userAgent: navigator.userAgent,
          qianniuVersion: window._vs ? window._vs.version : 'unknown',
          loginID: window._vs ? window._vs.loginID : null,
          timestamp: Date.now()
        }
      };

      socket.send(JSON.stringify(activationMessage));
      startHeartbeat();
      window.chatWebsocket = socket; // 存储到全局变量
    };

    socket.onmessage = async function(event) {
      let param = JSON.parse(event.data);

      // 处理服务器命令 - 使用参考项目的格式
      if (param.method === 'execute') {
        try {
          console.log('[千牛WebSocket] 执行命令:', param.expression);
          const res = await eval(param.expression);
          console.log('[千牛WebSocket] 命令执行结果:', res);

          // 使用参考项目的响应格式
          socket.send(JSON.stringify({
            type: 'execute',
            response: JSON.stringify(res)
          }));
        } catch (err) {
          console.error('[千牛WebSocket] 执行命令错误:', err);
          console.error('[千牛WebSocket] 错误详情:', {
            name: err.name,
            message: err.message,
            stack: err.stack
          });

          // 发送错误响应
          socket.send(JSON.stringify({
            type: 'execute',
            response: JSON.stringify({ error: err.message })
          }));
        }
      } else if (param.method === 'qianniu_api_call') {
        // 处理千牛API调用请求
        try {
          console.log('[千牛WebSocket] 执行千牛API调用:', param.apiParams);
          const apiResult = await window.callQianniuApi(param.apiParams);
          console.log('[千牛WebSocket] 千牛API调用结果:', apiResult);

          // 发送API调用结果
          socket.send(JSON.stringify({
            type: 'qianniu_api_response',
            requestId: param.requestId,
            response: JSON.stringify(apiResult)
          }));
        } catch (err) {
          console.error('[千牛WebSocket] 千牛API调用错误:', err);

          // 发送错误响应
          socket.send(JSON.stringify({
            type: 'qianniu_api_response',
            requestId: param.requestId,
            response: JSON.stringify({
              success: false,
              error: err.message,
              method: param.apiParams?.method || 'unknown'
            })
          }));
        }
      } else if (param.method === 'qianniu_insert_text') {
        // 处理发送文本到输入框请求
        try {
          console.log('[千牛WebSocket] 执行发送文本到输入框:', param.params);
          const result = await imsdk.invoke('application.insertText2Inputbox', param.params);
          console.log('[千牛WebSocket] 发送文本到输入框结果:', result);

          // 发送结果
          socket.send(JSON.stringify({
            type: 'qianniu_api_response',
            requestId: param.requestId,
            response: JSON.stringify({
              success: true,
              data: result,
              method: 'application.insertText2Inputbox'
            })
          }));
        } catch (err) {
          console.error('[千牛WebSocket] 发送文本到输入框错误:', err);

          // 发送错误响应
          socket.send(JSON.stringify({
            type: 'qianniu_api_response',
            requestId: param.requestId,
            response: JSON.stringify({
              success: false,
              error: err.message,
              method: 'application.insertText2Inputbox'
            })
          }));
        }
      } else if (param.type === 'pong') {
        // 心跳响应
        console.log('[千牛WebSocket] 心跳正常');
      }
    };

    socket.onclose = function(event) {
      console.log('[千牛WebSocket] 连接断开，3秒后重连...');
      clearInterval(heartbeatInterval);
      if (window.chatWebsocket === socket) {
        window.chatWebsocket = null; // 清理引用
      }
      setTimeout(window.___setupWebSocket, 3000);
    };

    socket.onerror = function(error) {
      console.error('[千牛WebSocket] 连接错误:', error);
    };

    function startHeartbeat() {
      heartbeatInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({
            type: 'ping',
            invitationId: window.QIANNIU_CONFIG.invitationId,
            timestamp: Date.now()
          }));
        } else {
          clearInterval(heartbeatInterval); // 如果连接断开，停止心跳
        }
      }, 30000); // 30秒心跳
    }
  };

  // 初始化WebSocket连接
  window.___setupWebSocket();

  // 千牛事件劫持 - 保持与测试脚本完全一致的逻辑
  if(typeof(window.___qnww) == 'undefined'){
    window.___qnww = window.onEventNotify;
    window.onEventNotify = function (sid, name, a, data){
      // 先调用原始函数，保持千牛正常功能
      window.___qnww(sid, name, a, data);

      try {
        name = JSON.parse(name);

        if(sid.indexOf('onConversationChange') >= 0){
          updateFromConversation(name);
          if (window.chatWebsocket && window.chatWebsocket.readyState === WebSocket.OPEN) {
            window.chatWebsocket.send(JSON.stringify({
              type: 'onConversationChange',
              invitationId: window.QIANNIU_CONFIG.invitationId,
              response: JSON.stringify({
                loginID: window._vs.loginID,
                conversation: name
              })
            }));
          }
          console.log('[千牛事件] onConversationChange:', name.nick);

        } else if(sid.indexOf('onConversationAdd') >= 0){
          updateFromConversation(name);
          if (window.chatWebsocket && window.chatWebsocket.readyState === WebSocket.OPEN) {
            window.chatWebsocket.send(JSON.stringify({
              type: 'onConversationAdd',
              invitationId: window.QIANNIU_CONFIG.invitationId,
              response: JSON.stringify({
                loginID: window._vs.loginID,
                conversation: name
              })
            }));
          }
          console.log('[千牛事件] onConversationAdd:', name.nick);

        } else if(sid.indexOf('onConversationClose') >= 0){
          updateFromConversation(name);
          if (window.chatWebsocket && window.chatWebsocket.readyState === WebSocket.OPEN) {
            window.chatWebsocket.send(JSON.stringify({
              type: 'onConversationClose',
              invitationId: window.QIANNIU_CONFIG.invitationId,
              response: JSON.stringify({
                loginID: window._vs.loginID,
                conversation: name
              })
            }));
          }
          console.log('[千牛事件] onConversationClose:', name.nick);

        } else if(sid.indexOf('OnChatDlgActive') >= 0){
          if (window.chatWebsocket && window.chatWebsocket.readyState === WebSocket.OPEN) {
            window.chatWebsocket.send(JSON.stringify({
              type: 'onChatDlgActive',
              invitationId: window.QIANNIU_CONFIG.invitationId,
              response: JSON.stringify({
                loginID: window._vs.loginID,
                conversation: window._vs.conversationID
              })
            }));
          }
          console.log('[千牛事件] OnChatDlgActive');
        }
      } catch (error) {
        console.error('[千牛事件] 处理事件时出错:', error);
      }
    };
  }
  // 千牛消息中心通知 - 需要先配置子账号接受通知
  if (typeof QN !== 'undefined') {
    QN.regEvent('bench.msgcenter.newmsgnotify', res => {
      if (window.chatWebsocket && window.chatWebsocket.readyState === WebSocket.OPEN) {
        window.chatWebsocket.send(JSON.stringify({
          type: 'messageCenterNotify',
          invitationId: window.QIANNIU_CONFIG.invitationId,
          response: res
        }));
      }
    });
  }

  // 新消息接收处理 - 只使用劫持机制
  if (typeof(window.onInvokeNotifyDelegate) == 'undefined') {
    console.log('[千牛注入] 使用劫持机制处理所有消息');

    // 监听新消息事件 - 只用于触发千牛获取消息，不主动调用
    if (typeof imsdk !== 'undefined') {
      imsdk.on(['im.singlemsg.onReceiveNewMsg'], cids => {
        cids.forEach(async cid => {
          console.log('[千牛消息] 收到新消息通知:', cid.ccode);

          // 如果不是当前会话，触发千牛获取消息
          if(cid.ccode !== window._conversationId.ccode) {
            try {
              console.log('[千牛消息] 触发千牛获取非当前会话消息:', cid.ccode);
              // 这个调用会被下面的劫持捕获
              await imsdk.invoke('im.singlemsg.GetNewMsg', {
                ccode: cid.ccode
              });
            } catch (error) {
              console.error('[千牛消息] 触发获取消息失败:', error);
            }
          }
          // 如果是当前会话，千牛会自动调用，我们只需要劫持
        });
      });
    }

    // 劫持所有 GetNewMsg 调用
    window.onInvokeNotifyDelegate = window.onInvokeNotify;
    window.onInvokeNotify = function(sid, status, response) {
      window.onInvokeNotifyDelegate(sid, status, response);

      try {
        var task = TASK_CACHE[sid];
        if (task && task.config.fn == 'im.singlemsg.GetNewMsg' && status === 0) {
          console.log('[千牛消息] 劫持到GetNewMsg调用:', task.config.param.ccode);

          if (window.chatWebsocket && window.chatWebsocket.readyState === WebSocket.OPEN) {
            window.chatWebsocket.send(JSON.stringify({
              type: 'receiveNewMsg',
              invitationId: window.QIANNIU_CONFIG.invitationId,
              response: response
            }));
            console.log('[千牛消息] 劫持发送成功');
          }
        }
      } catch (error) {
        console.error('[千牛消息] 劫持处理失败:', error);
      }
    };
  }

  // 工具函数：获取远程消息
  async function getRemoteMsg(ccode){
    try {
      var remoteMsg = await imsdk.invoke('im.singlemsg.GetRemoteHisMsg', {
        cid: {
          ccode,
          type: 1
        },
        count: 3,
        gohistory: 1,
        msgid: '-1',
        msgtime: '-1',
      });

      var buyer = { ccode };
      var msgs = remoteMsg.result.msgs;
      for(var idx = 0; idx < msgs.length; idx++){
        if(msgs[idx].loginid.nick != msgs[idx].fromid.nick){
          buyer = msgs[idx].fromid;
          break;
        }
      }
      return buyer;
    } catch (error) {
      console.error('[千牛工具] 获取远程消息失败:', error);
      return { ccode };
    }
  }

  // 工具函数：获取缓存会话
  function getCacheConv(ccode) {
    try {
      if (!window._buyerCache.has(ccode)) {
        updateBuyerCacheFromLocal();
      }
      return window._buyerCache.get(ccode);
    } catch (e) {
      console.error('[千牛缓存] 获取会话失败:', e.message);
    }
  }

  // 工具函数：从会话更新缓存
  function updateFromConversation(conv) {
    try {
      if (window._buyerCache.has(conv.ccode)) {
        return;
      }
      window._buyerCache.set(conv.ccode, conv);
    } catch (e) {
      console.error('[千牛缓存] 更新会话缓存失败:', e.message);
    }
  }

  // 工具函数：从本地数据更新买家缓存
  function updateBuyerCacheFromLocal() {
    try {
      const { _db: { msgDataMap } } = window;
      Array.from(msgDataMap).forEach(([ccode, messages]) => {
        if (window._buyerCache.has(ccode)) return;

        for (const message of messages) {
          const { ext: { receiver_nick: receiverNick, sender_nick: senderNick } = {},
                  originBanamaMessage: {toid, fromid} } = message;
          if (!senderNick || !receiverNick) continue;

          if (senderNick.includes(window._vs.loginID.nick)) {
            window._buyerCache.set(ccode, toid);
          }

          if (receiverNick.includes(window._vs.loginID.nick)) {
            window._buyerCache.set(ccode, fromid);
          }

          break; // 只处理第一条有效消息
        }
      });
    } catch (error) {
      console.error('[千牛缓存] 更新本地缓存失败:', error.message);
    }
  }

  // 消息发送函数 - 已改为TCP发送，此函数仅用于兼容性
  window.sendTextMessage = async function(content, targetBuyerNick) {
    console.log('[千牛注入] sendTextMessage被调用，但消息发送已改为TCP方式:', {
      content: content,
      targetBuyerNick: targetBuyerNick,
      note: '实际发送通过后端TCP服务器完成'
    });

    // 注意：消息发送现在通过后端TCP服务器处理
    // 这个函数保留只是为了避免前端调用错误
    return true;
  };

  // 移除图片和文件发送功能，只保留文本消息发送

  // 千牛API调用函数 - 基于真实SDK测试用例优化
  window.callQianniuApi = async function(apiParams) {
    console.log('[千牛API] 开始调用API:', apiParams);

    try {
      // 统一使用imsdk.invoke调用，这是千牛SDK的标准调用方式
      const result = await imsdk.invoke('application.invokeMTopChannelService', {
        method: apiParams.method,
        param: apiParams.param,
        httpMethod: apiParams.httpMethod || 'post',
        version: apiParams.version || '1.0'
      });

      console.log('[千牛API] 调用成功:', result);

      // 根据真实返回格式处理结果
      if (result && result.ret && result.ret[0] && result.ret[0].includes('SUCCESS')) {
        return {
          success: true,
          data: result.data || result,
          method: apiParams.method,
          api: result.api,
          ret: result.ret,
          version: result.v
        };
      } else {
        // 处理业务失败的情况
        return {
          success: false,
          error: result.ret ? result.ret.join(', ') : '调用失败',
          method: apiParams.method,
          data: result
        };
      }
    } catch (error) {
      console.error('[千牛API] 调用异常:', error);
      return {
        success: false,
        error: error.message || '网络异常',
        method: apiParams.method
      };
    }
  };

  // 千牛API快捷调用函数 - 基于真实SDK测试用例
  window.qianniuApiHelpers = {
    // 邀请下单
    inviteOrder: async function(encryptId, itemProps, options = {}) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.airisland.invite.order.send',
        param: {
          encryptId: encryptId,
          bizDomain: options.bizDomain ?? 'taobao',
          encrypType: options.encrypType ?? 'internal',
          buyerNick: options.buyerNick ?? '',
          itemProps: typeof itemProps === 'string' ? itemProps : JSON.stringify(itemProps)
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 查询近三个月订单
    queryRecentOrders: async function(securityBuyerUid, orderStatus = '') {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.trade.query',
        param: {
          orderStatus: orderStatus,
          securityBuyerUid: securityBuyerUid
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 查询历史订单
    queryHistoryOrders: async function(securityBuyerUid, pageNum = 1, pageSize = 10) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.trade.history.query',
        param: {
          securityBuyerUid: securityBuyerUid,
          pageNum: pageNum,
          pageSize: pageSize
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 发送商品卡片
    sendItemCard: async function(encryptId, batchItemIds, type = -1) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.item.recommend.send',
        param: {
          encryptId: encryptId,
          batchItemIds: typeof batchItemIds === 'string' ? batchItemIds : JSON.stringify(batchItemIds),
          type: type
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 查询订单物流信息
    queryOrderLogistics: async function(bizOrderId) {
      return await window.callQianniuApi({
        method: 'mtop.alibaba.fulfillment.printorder.consign.logistics.query',
        param: {
          bizOrderId: bizOrderId
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 查询商品记录（咨询/购买/足迹）
    queryItemRecord: async function(encryptId) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.item.record.query',
        param: {
          encryptId: encryptId
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 查询客户信息
    queryCustomerInfo: async function(encryptId) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.user.query',
        param: {
          encryptId: encryptId
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 搜索店铺商品
    searchShopItems: async function(options = {}) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.item.search',
        param: {
          pageSize: options.pageSize ?? 8,
          pageNo: options.pageNo ?? 1,
          keyWord: options.keyWord ?? '',
          sortKey: options.sortKey ?? 'sold',
          desc: options.desc ?? true,
          type: options.type ?? 0,
          queryGift: options.queryGift ?? false,
          encryptId: options.encryptId ?? ''
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 查询买家ID（通过昵称）
    searchBuyerId: async function(searchQuery) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.airisland.contact.search',
        param: {
          accessKey: 'qianniu-pc',
          accessSecret: 'qianniu-pc-secret',
          accountType: '3',
          searchQuery: searchQuery
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 查询店铺优惠券
    queryShopCoupons: async function() {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.user.shop.coupon.query',
        param: {},
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 发送优惠券
    sendCoupon: async function(name, activityId, description, encryptId) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cs.user.shop.coupon.send',
        param: {
          name: name,
          activityId: activityId,
          description: description,
          encryptId: encryptId
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 订单解密
    decryptOrder: async function(tid, bizType = 'qianniu', queryByTid = true) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.tid.decrypt',
        param: {
          bizType: bizType,
          tid: tid,
          queryByTid: queryByTid
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 获取店铺客服
    getShopCustomerService: async function(pageSize = 100) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cloudkefu.accountstatus.getbyid',
        param: {
          pageSize: pageSize
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 挂起会话
    setSuspend: async function(accountId, isSuspend = true, source = 1) {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cloudkefu.suspend.set',
        param: {
          account_id: accountId,
          source: source,
          is_suspend: isSuspend
        },
        httpMethod: 'post',
        version: '1.0'
      });
    },

    // 转接到个人客服
    forwardToPerson: async function(buyerId, toId, reason = '转接', appCid = '', buyerDomain = 'cntaobao', loginDomain = 'cntaobao') {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cloudkefu.forward',
        param: {
          buyerId: buyerId,
          toId: toId,
          reason: reason,
          options: JSON.stringify({
            appCid: appCid,
            buyerDomain: buyerDomain,
            loginDomain: loginDomain
          })
        },
        httpMethod: 'post',
        version: '3.0'
      });
    },

    // 转接到客服分组
    forwardToGroup: async function(buyerId, toId, groupId, reason = '转接', options = {}) {
      const defaultOptions = {
        appCid: '',
        forwardType: 2,
        charset: 'utf-8',
        exceptUsers: '',
        buyerDomain: 'cntaobao',
        loginDomain: 'cntaobao',
        ...options
      };

      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cloudkefu.forward',
        param: {
          buyerId: buyerId,
          toId: toId,
          reason: reason,
          options: JSON.stringify({
            groupId: groupId,
            ...defaultOptions
          })
        },
        httpMethod: 'post',
        version: '3.0'
      });
    },

    // 获取客服分组列表
    getDispatchGroups: async function(loginDomain = 'cntaobao') {
      return await window.callQianniuApi({
        method: 'mtop.taobao.qianniu.cloudkefu.dispatchgroups.get',
        param: {
          login_domain: loginDomain
        },
        httpMethod: 'post',
        version: '2.0'
      });
    },

    // 发送文本消息到输入框
    insertTextToInputbox: async function(uid, text) {
      try {
        console.log('[千牛API] 发送文本到输入框:', { uid, text });
        const result = await imsdk.invoke('application.insertText2Inputbox', {
          uid: uid,
          text: text
        });
        console.log('[千牛API] 发送文本到输入框成功:', result);
        return {
          success: true,
          data: result,
          method: 'application.insertText2Inputbox'
        };
      } catch (error) {
        console.error('[千牛API] 发送文本到输入框失败:', error);
        return {
          success: false,
          error: error.message || '发送失败',
          method: 'application.insertText2Inputbox'
        };
      }
    }
  };

  console.log('[千牛注入] 脚本初始化完成，千牛API功能已加载');
}`;
}

export default app;
