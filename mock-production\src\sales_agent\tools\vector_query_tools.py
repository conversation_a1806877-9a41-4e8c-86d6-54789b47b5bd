"""
通用向量查询工具 - 支持多品牌的向量化查询功能
"""
import logging
from typing import List, Dict, Any, Optional
from llama_index.core.tools import FunctionTool

from ..services.vector_store_service import VectorStoreService
from .base_tools import BaseTools

logger = logging.getLogger(__name__)


class VectorQueryTools(BaseTools):
    """通用向量查询工具类 - 支持多品牌"""

    def __init__(self, index=None, agent=None, **kwargs):
        super().__init__(index=index, agent=agent)
        self.vector_store = VectorStoreService()
        self.is_initialized = False
        logger.info("🔧 初始化通用向量查询工具")

    def initialize(self):
        """初始化向量存储"""
        try:
            if not self.is_initialized:
                # 只有在需要时才初始化
                logger.info("🔧 按需初始化向量查询工具...")
                self.vector_store.initialize()
                self.is_initialized = True
                logger.info("✅ 向量查询工具初始化完成")
        except Exception as e:
            logger.error(f"❌ 向量查询工具初始化失败: {e}")
            # 不抛出异常，允许工具继续工作
            logger.warning("⚠️ 向量查询工具将在离线模式下工作")

    def search_products_by_vector(self, query: str, brand_id: str = None, limit: int = 5, shop_id: str = None) -> str:
        """
        使用向量搜索产品信息

        Args:
            query: 搜索查询文本
            brand_id: 品牌ID，用于过滤特定品牌的产品
            limit: 返回结果数量限制，默认5
            shop_id: 店铺ID过滤，可选

        Returns:
            格式化的产品搜索结果文本
        """
        try:
            if not self.is_initialized:
                self.initialize()

            logger.info(f"🔍 向量搜索产品: {query} (品牌: {brand_id or '全部'})")

            # 构建过滤条件
            filters = {}
            if brand_id:
                filters['brand_id'] = brand_id
            if shop_id:
                filters['shop_id'] = shop_id

            # 执行向量搜索
            results = self.vector_store.search_products(query, limit, filters if filters else None)

            if not results:
                brand_text = f"{brand_id}品牌的" if brand_id else ""
                return f"未找到相关的{brand_text}产品信息。"

            # 格式化结果
            formatted_results = []
            brand_prefix = f"{brand_id} " if brand_id else ""
            for i, result in enumerate(results, 1):
                score_percent = f"{result.score * 100:.1f}%"
                formatted_results.append(f"""{brand_prefix}产品 {i} (相似度: {score_percent}):
{result.content}
---""")

            brand_text = f"{brand_id}品牌的" if brand_id else ""
            response = f"找到 {len(results)} 个相关的{brand_text}产品:\n\n" + "\n".join(formatted_results)
            logger.info(f"✅ 向量搜索产品完成，返回 {len(results)} 个结果")

            return response

        except Exception as e:
            logger.error(f"❌ 向量搜索产品失败: {e}")
            return f"搜索产品时出现错误: {str(e)}"

    def search_qa_knowledge_by_vector(self, query: str, brand_id: str = None, limit: int = 5, category_code: str = None, shop_id: str = None) -> str:
        """
        使用向量搜索问答知识库

        Args:
            query: 搜索查询文本
            brand_id: 品牌ID，用于过滤特定品牌的问答
            limit: 返回结果数量限制，默认5
            category_code: 分类代码过滤，可选
            shop_id: 店铺ID过滤，可选

        Returns:
            格式化的问答搜索结果文本
        """
        try:
            if not self.is_initialized:
                self.initialize()

            logger.info(f"🔍 向量搜索问答知识库: {query} (品牌: {brand_id or '全部'})")

            # 构建过滤条件
            filters = {}
            if brand_id:
                filters['brand_id'] = brand_id
            if category_code:
                filters['category_code'] = category_code
            if shop_id:
                filters['shop_id'] = shop_id

            # 执行向量搜索
            results = self.vector_store.search_qa_knowledge(query, limit, filters if filters else None)

            if not results:
                brand_text = f"{brand_id}品牌的" if brand_id else ""
                return f"未找到相关的{brand_text}问答信息。"

            # 格式化结果
            formatted_results = []
            brand_prefix = f"{brand_id} " if brand_id else ""
            for i, result in enumerate(results, 1):
                score_percent = f"{result.score * 100:.1f}%"
                formatted_results.append(f"""{brand_prefix}问答 {i} (相似度: {score_percent}):
{result.content}
---""")

            brand_text = f"{brand_id}品牌的" if brand_id else ""
            response = f"找到 {len(results)} 个相关的{brand_text}问答:\n\n" + "\n".join(formatted_results)
            logger.info(f"✅ 向量搜索问答知识库完成，返回 {len(results)} 个结果")

            return response

        except Exception as e:
            logger.error(f"❌ 向量搜索问答知识库失败: {e}")
            return f"搜索问答知识库时出现错误: {str(e)}"



    def intelligent_vector_qa(self, question: str, shop_id: str = None) -> str:
        """
        智能向量问答 - 综合搜索所有数据源

        Args:
            question: 用户问题
            shop_id: 店铺ID过滤，可选

        Returns:
            综合搜索结果和智能建议
        """
        try:
            if not self.is_initialized:
                self.initialize()

            logger.info(f"🤖 智能向量问答: {question}")

            # 执行综合搜索
            result = self.vector_store.intelligent_qa(question, shop_id)

            if not result['success']:
                return f"智能问答失败: {result['message']}"

            data = result['data']
            products = data['products']
            qa_knowledge = data['qa_knowledge']
            size_charts = data['size_charts']

            # 构建响应
            response_parts = []

            # 添加问答知识库结果（优先级最高）
            if qa_knowledge:
                response_parts.append("📚 相关问答知识:")
                for i, item in enumerate(qa_knowledge[:2], 1):
                    score_percent = f"{item['score'] * 100:.1f}%"
                    response_parts.append(f"{i}. (相似度: {score_percent})\n{item['content']}")
                response_parts.append("")

            # 添加产品信息
            if products:
                response_parts.append("🛍️ 相关产品信息:")
                for i, item in enumerate(products[:2], 1):
                    score_percent = f"{item['score'] * 100:.1f}%"
                    response_parts.append(f"{i}. (相似度: {score_percent})\n{item['content']}")
                response_parts.append("")

            # 添加尺码表信息
            if size_charts:
                response_parts.append("📏 相关尺码信息:")
                for i, item in enumerate(size_charts[:1], 1):
                    score_percent = f"{item['score'] * 100:.1f}%"
                    response_parts.append(f"{i}. (相似度: {score_percent})\n{item['content']}")
                response_parts.append("")

            # 添加智能建议
            if result['suggestion']:
                response_parts.append(f"💡 建议: {result['suggestion']}")

            if not response_parts:
                return "未找到相关信息，建议转人工客服处理。"

            response = "\n".join(response_parts)
            logger.info(f"✅ 智能向量问答完成")

            return response

        except Exception as e:
            logger.error(f"❌ 智能向量问答失败: {e}")
            return f"智能问答时出现错误: {str(e)}"



    def get_all_tools(self) -> List[FunctionTool]:
        """获取所有向量查询工具"""
        tools = [
            FunctionTool.from_defaults(
                fn=self.search_products_by_vector,
                name="search_products_by_vector",
                description="使用向量搜索产品信息，支持语义搜索，比关键词搜索更智能"
            ),
            FunctionTool.from_defaults(
                fn=self.search_qa_knowledge_by_vector,
                name="search_qa_knowledge_by_vector",
                description="使用向量搜索问答知识库，找到最相关的标准问答"
            ),
            FunctionTool.from_defaults(
                fn=self.intelligent_vector_qa,
                name="intelligent_vector_qa",
                description="智能向量问答，综合搜索所有数据源并提供智能建议"
            )
        ]

        logger.info(f"🛠️ 注册了 {len(tools)} 个向量查询工具")
        return tools
