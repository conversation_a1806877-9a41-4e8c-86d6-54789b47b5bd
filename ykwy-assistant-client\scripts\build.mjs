import { build } from 'vite'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

async function buildAll() {
  try {
    console.log('🔨 开始构建主进程...')
    await build({
      configFile: resolve(__dirname, '../configs/vite.main.ts'),
      mode: process.env.NODE_ENV || 'production'
    })
    console.log('✅ 主进程构建完成')

    console.log('🔨 开始构建 Preload 脚本...')
    await build({
      configFile: resolve(__dirname, '../configs/vite.preload.ts'),
      mode: process.env.NODE_ENV || 'production'
    })
    console.log('✅ Preload 脚本构建完成')

    console.log('🔨 开始构建渲染进程...')
    await build({
      configFile: resolve(__dirname, '../configs/vite.renderer.ts'),
      mode: process.env.NODE_ENV || 'production'
    })
    console.log('✅ 渲染进程构建完成')

    console.log('🎉 所有构建任务完成！')
  } catch (error) {
    console.error('❌ 构建失败:', error)
    process.exit(1)
  }
}

buildAll()
