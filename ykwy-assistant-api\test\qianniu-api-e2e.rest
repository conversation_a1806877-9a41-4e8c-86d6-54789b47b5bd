###
# 千牛API端到端测试
# 使用 REST Client 扩展或 Postman 运行
###

# 配置变量
@baseUrl = http://localhost:3002
@apiUrl = {{baseUrl}}/api/v1

# 连接和客户信息
@connectionId = ********-a2e1-7162-9e19-c97b62433696
@customerId = ********-a6a9-7630-b124-a4f2684cffa5

# 客服转接相关
@buyerId = *************
@toId = *************
@ccode = *************.1-*************.1#11001@cntaobao
@groupId = *********
@accountId = **********

# 业务数据
@orderId = 4646134045163268604
@itemIds = [************, ************, ************]
@searchQuery = tb783904683
@activityId = acfb71cd53684077a439a1a2f99a3ce3


###############################################################################
# 健康检查和连接测试
###############################################################################

### 测试健康检查
GET {{baseUrl}}/health

### 获取可用连接
GET {{apiUrl}}/qianniu-api/connections

### 获取客户列表 (success)
GET {{apiUrl}}/qianniu-api/customers?connectionId={{connectionId}}

###############################################################################
# 订单管理 API
###############################################################################

### 邀请下单 (SUCCESS)
POST {{apiUrl}}/qianniu-api/invite-order
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "customerId": "{{customerId}}",
  "itemProps": "[{\"itemId\":************,\"skuId\":5191249981449,\"quantity\":1,\"context\":{}}]",
  "buyerNick": "{{searchQuery}}",
  "bizDomain": "taobao",
  "encrypType": "internal"
}

### 查询近期订单 (SUCCESS)
POST {{apiUrl}}/qianniu-api/query-recent-orders
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "customerId": "{{customerId}}",
  "orderStatus": ""
}

### 查询历史订单 (FALSE)
POST {{apiUrl}}/qianniu-api/query-history-orders
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "customerId": "{{customerId}}",
  "pageNum": 1,
  "pageSize": 10
}

### 查询订单物流 (SUCCESS)
POST {{apiUrl}}/qianniu-api/query-order-logistics
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "bizOrderId": "{{orderId}}"
}

### 订单解密 (SUCCESS)
POST {{apiUrl}}/qianniu-api/decrypt-order
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "tid": "{{orderId}}",
  "bizType": "qianniu",
  "queryByTid": true
}

###############################################################################
# 商品管理 API
###############################################################################

### 发送商品卡片 (SUCCESS)
POST {{apiUrl}}/qianniu-api/send-item-card
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "customerId": "{{customerId}}",
  "batchItemIds": "{{itemIds}}",
  "type": -1
}

### 查询商品记录 (SUCCESS)
POST {{apiUrl}}/qianniu-api/query-item-record
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "customerId": "{{customerId}}"
}

### 搜索店铺商品 (SUCCESS)
POST {{apiUrl}}/qianniu-api/search-shop-items
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "customerId": "{{customerId}}",
  "pageSize": 8,
  "pageNo": 1,
  "keyWord": "",
  "sortKey": "sold",
  "desc": true,
  "type": 0,
  "queryGift": false
}

###############################################################################
# 用户管理 API
###############################################################################

### 查询客户信息 (SUCESS - 返回信息存疑)
POST {{apiUrl}}/qianniu-api/query-customer-info
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "customerId": "{{customerId}}"
}

### 搜索买家ID (SUCCESS)
POST {{apiUrl}}/qianniu-api/search-buyer-id
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "searchQuery": "{{searchQuery}}"
}

###############################################################################
# 优惠券管理 API
###############################################################################

### 查询店铺优惠券 (SUCCESS)
POST {{apiUrl}}/qianniu-api/query-shop-coupons
Content-Type: application/json

{
  "connectionId": "{{connectionId}}"
}

### 发送优惠券 (SUCESS)
POST {{apiUrl}}/qianniu-api/send-coupon
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "name": "测试优惠券",
  "activityId": "{{activityId}}",
  "description": "这是一个测试优惠券",
  "customerId": "{{customerId}}"
}

###############################################################################
# 客服操作 API
###############################################################################

### 挂起会话 (success)
POST {{apiUrl}}/qianniu-api/set-suspend
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "accountId": "{{accountId}}",
  "isSuspend": true,
  "source": 1
}

### 转接到个人客服 (success)
POST {{apiUrl}}/qianniu-api/forward-to-person
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "buyerId": "{{buyerId}}",
  "toId": "{{toId}}",
  "reason": "转接给专业客服",
  "appCid": "{{ccode}}",
  "buyerDomain": "cntaobao",
  "loginDomain": "cntaobao"
}

### 转接到客服分组 (success)
POST {{apiUrl}}/qianniu-api/forward-to-group
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "buyerId": "{{buyerId}}",
  "toId": "**********",
  "groupId": "{{groupId}}",
  "reason": "转接到技术支持组",
  "appCid": "*************.1-*********.1#11001@cntaobao",
  "forwardType": 2,
  "charset": "utf-8",
  "exceptUsers": "",
  "buyerDomain": "cntaobao",
  "loginDomain": "cntaobao"
}

### 获取店铺客服 (SUCESS)
POST {{apiUrl}}/qianniu-api/get-shop-customer-service
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "pageSize": 100
}

### 获取客服分组列表 (SUCESS)
POST {{apiUrl}}/qianniu-api/get-dispatch-groups
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "loginDomain": "cntaobao"
}

###############################################################################
# 消息发送 API
###############################################################################

### 发送文本消息到输入框 (SUCCESS)
POST {{apiUrl}}/qianniu-api/insert-text-to-inputbox
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "nickname": "{{searchQuery}}",
  "text": "Hello, World! 这是一条测试消息。"
}

### 发送文本消息到输入框 - 长文本测试
POST {{apiUrl}}/qianniu-api/insert-text-to-inputbox
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "nickname": "{{searchQuery}}",
  "text": "这是一条较长的测试消息，用于验证系统是否能够正确处理包含中文字符和特殊符号的文本内容。测试内容包括：1. 中文字符 2. 英文字符 3. 数字 123 4. 特殊符号 !@#$%^&*() 5. 表情符号 😊🎉✨"
}

### 发送文本消息到输入框 - 错误测试（缺少参数）
POST {{apiUrl}}/qianniu-api/insert-text-to-inputbox
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "nickname": "{{searchQuery}}"
}

### 通过注入脚本测试发送文本到输入框（需要在千牛客户端控制台执行）
# 在千牛客户端控制台执行以下JavaScript代码：
# window.qianniuApiHelpers.insertTextToInputbox('cntaobao{{searchQuery}}', 'Hello from injected script!');

###############################################################################
# 通用API调用
###############################################################################

### 通用API调用 - 测试搜索买家ID
POST {{apiUrl}}/qianniu-api/call-api
Content-Type: application/json

{
  "connectionId": "{{connectionId}}",
  "method": "mtop.taobao.qianniu.airisland.contact.search",
  "param": {
    "accessKey": "qianniu-pc",
    "accessSecret": "qianniu-pc-secret",
    "accountType": "3",
    "searchQuery": "{{searchQuery}}"
  },
  "httpMethod": "post",
  "version": "1.0"
}
