import { app } from 'electron'
import * as path from 'path'

/**
 * 判断是否为开发环境
 */
export const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

/**
 * 获取资源路径
 */
export function getAssetPath(...paths: string[]): string {
  const RESOURCES_PATH = app.isPackaged
    ? path.join(
        (process as NodeJS.Process & { resourcesPath: string }).resourcesPath,
        'assets'
      )
    : path.join(process.cwd(), 'assets')

  return path.join(RESOURCES_PATH, ...paths)
}

// 获取应用路径
export function getAppPath(): string {
  return isDev ? process.cwd() : app.getAppPath()
}

// 获取用户数据目录
export function getUserDataPath(): string {
  return app.getPath('userData')
}

// 日志工具
export const logger = {
  info: (message: string, ...args: unknown[]) => {
    console.log(`[MAIN] [INFO] ${message}`, ...args)
  },
  warn: (message: string, ...args: unknown[]) => {
    console.warn(`[MAIN] [WARN] ${message}`, ...args)
  },
  error: (message: string, ...args: unknown[]) => {
    console.error(`[MAIN] [ERROR] ${message}`, ...args)
  },
  debug: (message: string, ...args: unknown[]) => {
    if (isDev) {
      console.debug(`[MAIN] [DEBUG] ${message}`, ...args)
    }
  }
}
