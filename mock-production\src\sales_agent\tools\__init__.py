"""
销售工具集 - 拆分为多个模块，便于维护和扩展
"""
from typing import List, Dict, Any, Optional
from llama_index.core.tools import FunctionTool

from .service_tools import ServiceTools
from .dialogue_tools import DialogueTools
from .strategy_tools import StrategyTools
from .order_tools import OrderTools
from .commerce_tools import CommerceTools
from .coupon_tools import CouponTools
class SalesTools(ServiceTools, DialogueTools, StrategyTools, OrderTools, CommerceTools, CouponTools):
    """销售工具集整合类 - ykwy通用AI服务智能体工具"""

    def get_tools(self) -> List[FunctionTool]:
        """获取所有销售工具，包括订单管理工具"""
        return [
            # 服务工具
            FunctionTool.from_defaults(
                fn=self.identify_service_link,
                name="identify_service_link",
                description="识别用户发送的服务链接，如果用户消息包含淘宝服务链接，返回对应的服务信息。参数：user_message（用户原始消息）。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_service_list,
                name="get_service_list",
                description="获取我们提供的所有服务列表，返回包含item_id的服务信息。我们主要提供三个AI服务：PPT制作、视频创作、智能写作。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_service_details,
                name="get_service_details",
                description="根据item_id获取具体服务的详细信息JSON，包括功能特点、适用场景、目标用户、价格等完整信息。参数item_id从get_service_list的结果中获取。"
            ),

            # 对话工具
            FunctionTool.from_defaults(
                fn=self.get_similar_dialogues,
                name="get_similar_dialogues",
                description="从真实对话数据中查找相似问题的回答，返回前5条作为参考，学习真实客服的回复风格。参数：user_message（用户原始消息）。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_standard_answers,
                name="get_standard_answers",
                description="从按销售阶段设计的标准QA库中查找最合适的回答。参数：user_message（用户原始消息），sales_stage（可选，销售阶段：初次接触/需求确认/方案展示/合作引导等）。"
            ),

            # 策略工具
            FunctionTool.from_defaults(
                fn=self.decide_sales_strategy,
                name="decide_sales_strategy",
                description="使用LLM智能分析客户意图、对话阶段和销售时机，决定下一步策略：是否引导加微信、继续了解需求、还是展示产品价值。参数user_message必须是用户的原始消息内容。"
            ),

            # 订单工具
            FunctionTool.from_defaults(
                fn=self.get_all_orders,
                name="get_all_orders",
                description="获取当前客户的所有订单列表。当客户询问'我的订单'、'订单查询'、'查看订单'等时使用。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_order_details,
                name="get_order_details",
                description="根据订单号获取订单的详细信息，包括商品、价格、状态等。当客户提供具体订单号查询详情时使用。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_order_logistics,
                name="get_order_logistics",
                description="根据订单号查询订单的物流状态和配送进度。当客户询问'物流'、'快递'、'什么时候到'等时使用。"
            ),

            # 商务工具
            FunctionTool.from_defaults(
                fn=self.send_item_card,
                name="send_item_card",
                description="发送商品卡片给客户。参数：item_ids（必填，商品ID列表或单个ID，最多3个），encrypt_id（可选，客户加密ID）。使用前需要先调用get_service_list获取商品ID。"
            ),
            FunctionTool.from_defaults(
                fn=self.send_purchase_link,
                name="send_purchase_link",
                description="发送下单链接给客户。参数：item_id（必填，商品ID），sku_id（可选，默认'5208588653629'），quantity（可选，商品数量，默认1）。使用前需要先调用get_service_list获取商品ID。"
            ),

            # 优惠券工具
            FunctionTool.from_defaults(
                fn=self.get_shop_coupons,
                name="get_shop_coupons",
                description="获取店铺所有可用优惠券信息。当需要了解可发送的优惠券列表时调用此工具。无需参数。"
            ),
            FunctionTool.from_defaults(
                fn=self.send_coupon,
                name="send_coupon",
                description="发送优惠券给客户。参数：activity_id（必填，优惠券活动ID），coupon_name（可选，优惠券名称），description（可选，优惠券描述）。使用前需要先调用get_shop_coupons获取优惠券活动ID。"
            ),



        ]
