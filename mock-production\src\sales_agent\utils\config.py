"""
配置管理模块 - 完全从环境变量读取
"""
import os
from typing import Optional


class Settings:
    """应用配置 - 直接从环境变量读取"""

    def __init__(self):
        # 加载 .env 文件
        self._load_env_file()

    def _load_env_file(self):
        """加载 .env 文件到环境变量"""
        env_file = ".env"
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        # 移除引号
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        os.environ[key] = value

    @property
    def openai_api_key(self) -> str:
        """OpenAI API密钥"""
        return os.environ.get("OPENAI_API_KEY")

    @property
    def openai_base_url(self) -> str:
        """OpenAI API基础URL"""
        # 支持多种环境变量名称
        base_url = os.environ.get("OPENAI_API_BASE")

        # 确保 URL 以 /v1 结尾（如果不是完整路径）
        if not base_url.endswith('/v1') and not base_url.endswith('/embeddings'):
            base_url = base_url.rstrip('/') + '/v1'

        return base_url

    @property
    def debug(self) -> bool:
        """调试模式"""
        return os.environ.get("DEBUG", "false").lower() in ("true", "1", "yes")

    @property
    def data_dir(self) -> str:
        """数据目录"""
        return os.environ.get("DATA_DIR", "data")

    @property
    def ykwy_api_base_url(self) -> str:
        """YKWY API基础URL"""
        return os.environ.get("YKWY_API_BASE_URL", "https://ykwy-api.wuyoutansuo.com")

    @property
    def ykwy_api_token(self) -> str:
        """YKWY API访问令牌"""
        return os.environ.get("YKWY_API_TOKEN", "PoNyP4f0zYDWuXg8NytR")

    @property
    def chroma_db_path(self) -> str:
        """ChromaDB数据库路径"""
        return os.environ.get("CHROMA_DB_PATH", "./data/chroma_db")

    @property
    def api_host(self) -> str:
        """API服务主机"""
        return os.environ.get("API_HOST", "0.0.0.0")

    @property
    def api_port(self) -> int:
        """API服务端口"""
        return int(os.environ.get("API_PORT", "8000"))

    @property
    def chunk_size(self) -> int:
        """文档分块大小"""
        return int(os.environ.get("CHUNK_SIZE", "1024"))

    @property
    def chunk_overlap(self) -> int:
        """文档分块重叠"""
        return int(os.environ.get("CHUNK_OVERLAP", "200"))

    @property
    def similarity_top_k(self) -> int:
        """相似度搜索返回数量"""
        return int(os.environ.get("SIMILARITY_TOP_K", "5"))

    @property
    def agent_name(self) -> str:
        """代理名称"""
        return os.environ.get("AGENT_NAME", "淘宝产品销售助手")

    @property
    def agent_description(self) -> str:
        """代理描述"""
        return os.environ.get("AGENT_DESCRIPTION", "专业的AI产品销售顾问，帮助客户选择最适合的AI工具")

    @property
    def max_iterations(self) -> int:
        """最大迭代次数"""
        return int(os.environ.get("MAX_ITERATIONS", "10"))

    def print_all_env_vars(self):
        """打印所有环境变量（启动时调试用）"""
        print("\n" + "="*60)
        print("🔧 环境变量配置信息")
        print("="*60)

        # 敏感信息需要脱敏
        sensitive_keys = ['OPENAI_API_KEY', 'LOKI_PASSWORD', 'JWT_SECRET']

        # 按类别分组显示
        categories = {
            "🤖 AI服务配置": [
                'OPENAI_API_KEY', 'OPENAI_API_BASE', 'OPENAI_BASE_URL'
            ],
            "🌐 API服务配置": [
                'API_HOST', 'API_PORT', 'QIANNIU_API_BASE_URL'
            ],
            "📊 日志配置": [
                'LOKI_URL', 'LOKI_USERNAME', 'LOKI_PASSWORD', 'LOKI_SYNC_MODE', 'LOKI_ASYNC_MODE'
            ],
            "🔧 应用配置": [
                'NODE_ENV', 'DEBUG', 'DATA_DIR', 'CHUNK_SIZE', 'CHUNK_OVERLAP',
                'SIMILARITY_TOP_K', 'MAX_ITERATIONS', 'AGENT_NAME'
            ],
            "🌐 YKWY API配置": [
                'YKWY_API_BASE_URL', 'YKWY_API_TOKEN', 'CHROMA_DB_PATH'
            ],
            "🐳 Docker配置": [
                'PYTHONPATH', 'PYTHONUNBUFFERED'
            ]
        }

        for category, keys in categories.items():
            print(f"\n{category}:")
            for key in keys:
                value = os.environ.get(key, '未设置')
                if key in sensitive_keys and value != '未设置':
                    # 脱敏处理
                    if len(value) > 8:
                        masked_value = value[:4] + '*' * (len(value) - 8) + value[-4:]
                    else:
                        masked_value = '*' * len(value)
                    print(f"   {key}: {masked_value}")
                else:
                    print(f"   {key}: {value}")

        # 显示其他相关环境变量
        print(f"\n🔍 其他环境变量:")
        other_vars = []
        for key, value in os.environ.items():
            if not any(key in cat_keys for cat_keys in categories.values()):
                if key.startswith(('SALES_', 'YKWY_', 'MOCK_', 'AI_')):
                    other_vars.append((key, value))

        if other_vars:
            for key, value in sorted(other_vars):
                print(f"   {key}: {value}")
        else:
            print("   无其他相关环境变量")

        print("\n" + "="*60)
        print("✅ 环境变量加载完成")
        print("="*60 + "\n")


# 全局配置实例
settings = Settings()
