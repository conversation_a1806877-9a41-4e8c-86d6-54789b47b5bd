"""
FastAPI应用
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import logging
import os

from ..core.agent import SalesAgent
from ..core.brand_manager import get_brand_manager
from ..utils.config import settings
from ..utils.logger import setup_detailed_logging, log_user_interaction, unified_logger
from ..utils.loki import loki_service
from ..tools.brand_vector_tools import brand_vector_factory
from .vector_routes import router as vector_router

# 设置详细日志
setup_detailed_logging()
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="淘宝产品信息智能销售代理",
    description="基于LlamaIndex的AI产品销售助手",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 包含向量路由
app.include_router(vector_router)

# 全局品牌向量工具工厂实例
# vector_service = VectorStoreService()  # 已由brand_vector_factory替代

# 全局品牌管理器实例
brand_manager = None


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str
    brand_id: Optional[str] = "ykwy"  # 品牌ID，默认为易客无忧
    session_id: Optional[str] = None
    conversation_id: Optional[str] = None
    connection_id: Optional[str] = None  # 千牛连接ID
    customer_id: Optional[str] = None    # 客户ID
    buyer_nick: Optional[str] = None  # 客户昵称


class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    session_id: Optional[str] = None


class ConversationMessage(BaseModel):
    """对话消息模型"""
    role: str  # "customer" 或 "agent"
    content: str
    timestamp: Optional[str] = None


class DialogueMessage(BaseModel):
    """对话消息模型"""
    speaker: str  # "customer" 或 "service"
    message: str


class RecommendationRequest(BaseModel):
    """推荐请求模型"""
    conversation_history: List[DialogueMessage]
    conversation_id: Optional[str] = None
    brand_id: Optional[str] = "qinggong"  # 默认轻功体育
    shop_id: Optional[str] = None
    customer_type: Optional[str] = None  # 客户类型
    customer_personality: Optional[str] = None  # 客户性格特征


class RecommendationResponse(BaseModel):
    """推荐响应模型"""
    recommendations: List[str]
    conversation_id: Optional[str] = None
    context_analysis: Optional[str] = None  # 对话上下文分析


def _analyze_dialogue_context(conversation_history: List[DialogueMessage], customer_type: str = None, customer_personality: str = None) -> str:
    """分析对话上下文，识别对话阶段和客户意图"""
    if not conversation_history:
        return "初始接触阶段"

    last_messages = conversation_history[-3:]  # 分析最近3条消息
    customer_messages = [msg.message for msg in last_messages if msg.speaker == "customer"]

    # 关键词分析
    keywords_analysis = {
        "价格关注": ["多少钱", "价格", "费用", "收费", "贵", "便宜", "优惠"],
        "产品咨询": ["怎么做", "能做", "包含", "功能", "效果", "质量"],
        "决策犹豫": ["考虑", "想想", "再说", "不确定", "比较"],
        "购买意向": ["怎么买", "下单", "付款", "购买", "要", "需要"],
        "售后关注": ["保障", "修改", "退款", "服务", "售后"],
        "时间紧急": ["急", "快", "马上", "立即", "尽快", "时间"]
    }

    detected_intents = []
    for intent, keywords in keywords_analysis.items():
        if any(keyword in " ".join(customer_messages) for keyword in keywords):
            detected_intents.append(intent)

    # 构建分析结果
    analysis_parts = []
    if detected_intents:
        analysis_parts.append(f"客户意图: {', '.join(detected_intents)}")

    if customer_type:
        analysis_parts.append(f"客户类型: {customer_type}")

    if customer_personality:
        analysis_parts.append(f"性格特征: {customer_personality}")

    # 判断对话阶段
    message_count = len(conversation_history)
    if message_count <= 2:
        stage = "初始接触阶段"
    elif message_count <= 6:
        stage = "需求了解阶段"
    elif message_count <= 10:
        stage = "方案介绍阶段"
    else:
        stage = "成交跟进阶段"

    analysis_parts.append(f"对话阶段: {stage}")

    return " | ".join(analysis_parts)


def _build_recommendation_prompt(context_text: str, dialogue_analysis: str, customer_type: str = None, customer_personality: str = None, brand_id: str = "qinggong") -> str:
    """构建智能推荐提示词"""

    brand_context = {
        "qinggong": "轻功体育 - 专业运动服装品牌，注重产品质量和运动体验",
        "ykwy": "易客无忧 - AI工具服务商，提供PPT制作、视频创作、写作助手等服务"
    }

    brand_info = brand_context.get(brand_id, "专业服务提供商")

    prompt = f"""
你是{brand_info}的专业客服顾问，请基于以下信息生成3个不同的客服回复建议。

## 对话上下文分析
{dialogue_analysis}

## 完整对话历史
{context_text}

## 生成要求
请生成3个专业的客服回复建议，每个建议应该：

1. **针对性强**: 根据客户当前的具体问题和需求
2. **策略不同**: 三个建议采用不同的沟通策略和角度
3. **专业友好**: 保持专业水准，语调亲切自然
4. **推进对话**: 引导对话向积极方向发展，促进成交

## 特殊指导
"""

    # 根据客户类型添加特殊指导
    if customer_type:
        type_guidance = {
            "创业者": "注重ROI和效率，强调专业性和时间价值",
            "企业用户": "关注服务保障和批量优惠，突出专业团队",
            "个人用户": "注重性价比和使用体验，提供贴心服务",
            "学生": "强调优惠价格和学习价值，提供教育支持"
        }
        if customer_type in type_guidance:
            prompt += f"- 客户类型指导: {type_guidance[customer_type]}\n"

    if customer_personality:
        prompt += f"- 性格特征考虑: {customer_personality}\n"

    prompt += """
## 输出格式
请直接返回3个建议，每个建议一行，不需要编号或其他格式。
每个建议应该是完整的客服回复，可以直接使用。
"""

    return prompt


def _get_smart_default_recommendations(dialogue_analysis: str, customer_type: str = None) -> List[str]:
    """获取智能默认推荐"""

    # 根据对话分析选择合适的默认推荐
    if "价格关注" in dialogue_analysis:
        return [
            "我理解您对价格的关注，我们的服务性价比很高，而且现在有优惠活动，我详细给您介绍一下～",
            "关于价格，我们有不同的套餐可以选择，我可以根据您的具体需求推荐最合适的方案",
            "价格方面我们很有竞争力，而且服务质量有保障，您可以先了解一下我们的服务内容"
        ]
    elif "产品咨询" in dialogue_analysis:
        return [
            "关于您咨询的这个问题，我来详细为您解答，我们在这方面很专业",
            "这个我们完全可以做到，而且效果会很好，我给您详细介绍一下我们的优势",
            "您的需求我们经常遇到，有很成熟的解决方案，我可以给您看一些案例"
        ]
    elif "决策犹豫" in dialogue_analysis:
        return [
            "我理解您需要考虑，这是很正常的。我可以给您提供更多信息帮助您做决定",
            "没关系，您可以慢慢考虑。我们有很多成功案例，要不我给您分享几个参考一下？",
            "考虑是应该的，毕竟要选择最适合的。您还有什么疑虑吗？我来帮您分析一下"
        ]
    else:
        return [
            "感谢您的咨询，我来为您详细解答这个问题",
            "我理解您的需求，让我为您提供专业的建议",
            "根据您的情况，我来为您推荐最合适的解决方案"
        ]


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global brand_manager

    try:
        # 打印环境变量配置（调试用）
        print("\n" + "="*60)
        print("🔧 应用启动 - 环境变量检查")
        print("="*60)
        settings.print_all_env_vars()

        # 记录Loki服务状态
        if loki_service.is_enabled():
            print(f"✅ Loki日志服务已启用")
            print(f"   URL: {loki_service.url}")
            print(f"   用户名: {loki_service.username}")
            unified_logger.info('Loki日志服务已启用', {
                'service': 'loki',
                'url': loki_service.url,
                'server_name': loki_service.server_name
            })
        else:
            print(f"❌ Loki日志服务未启用")
            print(f"   LOKI_URL: {os.environ.get('LOKI_URL', '未设置')}")
            print(f"   LOKI_USERNAME: {os.environ.get('LOKI_USERNAME', '未设置')}")
            print(f"   LOKI_PASSWORD: {'已设置' if os.environ.get('LOKI_PASSWORD') else '未设置'}")
            unified_logger.warn('Loki日志服务未启用，请检查环境变量配置', {
                'service': 'loki',
                'required_vars': ['LOKI_URL', 'LOKI_USERNAME', 'LOKI_PASSWORD']
            })

        unified_logger.info("正在初始化品牌管理器...", {'component': 'brand_manager'})
        logger.info("正在初始化品牌管理器...")
        brand_manager = get_brand_manager()

        unified_logger.info("品牌管理器初始化完成", {
            'component': 'brand_manager',
            'status': 'ready',
            'brands': brand_manager.get_available_brands()
        })
        logger.info(f"品牌管理器初始化完成，支持品牌: {brand_manager.get_available_brands()}")

    except Exception as e:
        unified_logger.error("初始化品牌管理器失败", {'component': 'brand_manager'}, e)
        logger.error(f"初始化品牌管理器失败: {e}")
        raise


@app.get("/")
async def root():
    """根路径 - 返回Web界面"""
    if os.path.exists("static/index.html"):
        return FileResponse("static/index.html")
    return {
        "message": "淘宝产品信息智能销售代理API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/dashboard")
async def vector_dashboard():
    """向量数据可视化仪表板"""
    if os.path.exists("static/vector_dashboard.html"):
        return FileResponse("static/vector_dashboard.html")
    return {
        "message": "向量数据可视化仪表板",
        "error": "dashboard文件不存在"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "brand_manager_ready": brand_manager is not None,
        "available_brands": brand_manager.get_available_brands() if brand_manager else []
    }


@app.get("/brands")
async def get_brands():
    """获取所有可用品牌信息"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    brands = []
    for brand_id in brand_manager.get_available_brands():
        brand_info = brand_manager.get_brand_info(brand_id)
        brands.append({
            "brand_id": brand_id,
            **brand_info
        })

    return {"brands": brands}


@app.get("/brands/{brand_id}")
async def get_brand_info(brand_id: str):
    """获取指定品牌信息"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    brand_info = brand_manager.get_brand_info(brand_id)
    if not brand_info or brand_info.get('name') == '未知品牌':
        raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

    return {
        "brand_id": brand_id,
        **brand_info
    }


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口（支持多品牌）"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info("🌐 [API] 收到聊天请求")
        logger.info(f"   🏢 品牌ID: {request.brand_id}")
        logger.info(f"   📝 消息: {request.message}")
        logger.info(f"   🆔 会话ID: {request.session_id}")
        logger.info(f"   🔗 连接ID: {request.connection_id}")
        logger.info(f"   👤 客户ID: {request.customer_id}")
        logger.info(f"   📛 客户昵称: {request.buyer_nick}")

        # 使用品牌管理器处理消息
        response = await brand_manager.chat(
            brand_id=request.brand_id,
            message=request.message,
            conversation_id=request.conversation_id,
            connection_id=request.connection_id,
            customer_id=request.customer_id,
            customer_nick=request.buyer_nick
        )

        # 记录用户交互
        log_user_interaction(f"[{request.brand_id}] {request.message}", response)

        return ChatResponse(
            response=response,
            session_id=request.session_id
        )

    except Exception as e:
        logger.error(f"❌ [API] 聊天处理错误: {e}")
        raise HTTPException(status_code=500, detail=f"处理聊天消息时出错: {str(e)}")


@app.get("/conversation/{conversation_id}/history")
async def get_conversation_history(conversation_id: str, brand_id: str = "ykwy"):
    """获取对话历史"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        history = brand_manager.get_conversation_history(brand_id, conversation_id)
        return {
            "conversation_id": conversation_id,
            "brand_id": brand_id,
            "message_count": len(history),
            "history": history
        }

    except Exception as e:
        logger.error(f"获取对话历史错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话历史时出错: {str(e)}")


@app.post("/conversation/{conversation_id}/reset")
async def reset_conversation(conversation_id: str, brand_id: str = "ykwy"):
    """重置对话记忆"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        success = brand_manager.reset_conversation(brand_id, conversation_id)
        if success:
            return {"message": f"品牌 {brand_id} 的对话 {conversation_id} 记忆已重置"}
        else:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

    except Exception as e:
        logger.error(f"重置对话记忆错误: {e}")
        raise HTTPException(status_code=500, detail=f"重置对话记忆时出错: {str(e)}")


@app.post("/recommendations", response_model=RecommendationResponse)
async def generate_recommendations(request: RecommendationRequest):
    """生成AI智能推荐回复

    基于对话历史，生成3个推荐的客服回复，指导客服下一步应该说什么
    """
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info("🤖 [API] 收到AI推荐生成请求")
        logger.info(f"   🆔 会话ID: {request.conversation_id}")
        logger.info(f"   🏷️ 品牌ID: {request.brand_id}")
        logger.info(f"   🏪 店铺ID: {request.shop_id}")
        logger.info(f"   📝 对话历史长度: {len(request.conversation_history)}")
        logger.info(f"   👤 客户类型: {request.customer_type}")
        logger.info(f"   🎭 客户性格: {request.customer_personality}")

        # 构建对话上下文
        conversation_context = []
        for msg in request.conversation_history:
            role_prefix = "客户" if msg.speaker == "customer" else "客服"
            conversation_context.append(f"{role_prefix}: {msg.message}")

        context_text = "\n".join(conversation_context)

        # 分析对话阶段和客户意图
        dialogue_analysis = _analyze_dialogue_context(request.conversation_history, request.customer_type, request.customer_personality)

        # 构建智能推荐生成的提示词
        recommendation_prompt = _build_recommendation_prompt(
            context_text,
            dialogue_analysis,
            request.customer_type,
            request.customer_personality,
            request.brand_id
        )

        # 一次性生成3个智能推荐
        try:
            # 使用统一的会话ID
            session_id = request.conversation_id or "recommendation_session"

            # 使用指定品牌生成推荐
            brand_id = request.brand_id or "qinggong"
            response = await brand_manager.chat(brand_id, recommendation_prompt, conversation_id=session_id)

            # 解析响应，期望得到3行推荐
            lines = [line.strip() for line in response.strip().split('\n') if line.strip()]

            # 过滤掉编号和格式化文本，只保留推荐内容
            recommendations = []
            for line in lines:
                # 移除可能的编号前缀 (1. 2. 3. 或 - 等)
                cleaned_line = line
                if line.startswith(('1.', '2.', '3.', '- ', '• ', '* ')):
                    cleaned_line = line[2:].strip()
                elif line.startswith(('1、', '2、', '3、')):
                    cleaned_line = line[2:].strip()

                if cleaned_line and len(cleaned_line) > 10:  # 确保推荐内容有意义
                    recommendations.append(cleaned_line)

            logger.info(f"✅ 一次性生成 {len(recommendations)} 个推荐")

        except Exception as e:
            logger.warning(f"生成推荐失败: {e}")
            # 使用智能默认推荐
            recommendations = _get_smart_default_recommendations(
                dialogue_analysis, request.customer_type
            )

        # 确保至少有3个推荐
        while len(recommendations) < 3:
            smart_defaults = _get_smart_default_recommendations(dialogue_analysis, request.customer_type)
            needed_count = 3 - len(recommendations)
            recommendations.extend(smart_defaults[:needed_count])

        # 只取前3个
        recommendations = recommendations[:3]

        logger.info(f"✅ [API] 成功生成 {len(recommendations)} 个AI推荐")

        return RecommendationResponse(
            recommendations=recommendations,
            conversation_id=request.conversation_id,
            context_analysis=dialogue_analysis
        )

    except Exception as e:
        logger.error(f"❌ [API] 推荐生成错误: {e}")
        raise HTTPException(status_code=500, detail=f"生成推荐时出错: {str(e)}")


def _get_smart_default_recommendations(dialogue_analysis: str, customer_type: str = None) -> List[str]:
    """获取智能默认推荐"""

    # 根据对话分析选择合适的默认推荐
    if "价格关注" in dialogue_analysis:
        return [
            "我理解您对价格的关注，我们的服务性价比很高，而且现在有优惠活动，我详细给您介绍一下～",
            "关于价格，我们有不同的套餐可以选择，我可以根据您的具体需求推荐最合适的方案",
            "价格方面我们很有竞争力，而且服务质量有保障，您可以先了解一下我们的服务内容"
        ]
    elif "产品咨询" in dialogue_analysis:
        return [
            "关于您咨询的这个问题，我来详细为您解答，我们在这方面很专业",
            "这个我们完全可以做到，而且效果会很好，我给您详细介绍一下我们的优势",
            "您的需求我们经常遇到，有很成熟的解决方案，我可以给您看一些案例"
        ]
    elif "决策犹豫" in dialogue_analysis:
        return [
            "我理解您需要考虑，这是很正常的。我可以给您提供更多信息帮助您做决定",
            "没关系，您可以慢慢考虑。我们有很多成功案例，要不我给您分享几个参考一下？",
            "考虑是应该的，毕竟要选择最适合的。您还有什么疑虑吗？我来帮您分析一下"
        ]
    else:
        return [
            "感谢您的咨询，我来为您详细解答这个问题",
            "我理解您的需求，让我为您提供专业的建议",
            "根据您的情况，我来为您推荐最合适的解决方案"
        ]


@app.post("/rebuild-index")
async def rebuild_index(brand_id: str = "ykwy"):
    """重建索引"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info(f"开始重建品牌 {brand_id} 的索引...")
        agent = brand_manager.get_agent(brand_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

        agent.build_index(force_rebuild=True)
        agent.initialize_agent()
        logger.info(f"品牌 {brand_id} 索引重建完成")

        return {"message": f"品牌 {brand_id} 索引重建完成"}

    except Exception as e:
        logger.error(f"重建索引错误: {e}")
        raise HTTPException(status_code=500, detail=f"重建索引时出错: {str(e)}")


@app.get("/debug/dialogues")
async def get_debug_dialogues():
    """获取所有对话数据用于调试"""
    try:
        import json
        from pathlib import Path

        dialogues = []
        data_dir = Path("data")

        # 遍历所有产品目录
        for product_dir in ["ai-ppt-maker", "ai-video-creator", "ai-writing-assistant"]:
            dialogue_file = data_dir / product_dir / "customer-dialogues.json"
            if dialogue_file.exists():
                with open(dialogue_file, 'r', encoding='utf-8') as f:
                    product_dialogues = json.load(f)

                for dialogue in product_dialogues:
                    dialogues.append({
                        "id": dialogue["dialogueId"],
                        "product": product_dir,
                        "customerType": dialogue["customerType"],
                        "dialogue": dialogue["dialogue"]
                    })

        return {"dialogues": dialogues}

    except Exception as e:
        logger.error(f"获取对话数据错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话数据时出错: {str(e)}")


class VectorSyncResponse(BaseModel):
    """向量同步响应模型"""
    success: bool
    message: str
    data: Optional[dict] = None


@app.post("/vector/sync", response_model=VectorSyncResponse)
async def sync_vector_data(brand_id: Optional[str] = None):
    """同步向量数据库 - HTTP端点，支持按品牌同步"""
    try:
        if brand_id:
            logger.info(f"🚀 开始同步品牌 {brand_id} 的向量数据...")
            result = brand_vector_factory.sync_brand_data(brand_id)
        else:
            logger.info("🚀 开始同步所有品牌的向量数据...")
            # 同步所有支持的品牌
            all_results = {}
            total_success = True
            total_message_parts = []

            # 目前只支持轻功体育
            supported_brands = ['qinggong']

            for brand in supported_brands:
                brand_result = brand_vector_factory.sync_brand_data(brand)
                all_results[brand] = brand_result
                if not brand_result['success']:
                    total_success = False
                total_message_parts.append(f"{brand}: {brand_result['message']}")

            result = {
                'success': total_success,
                'message': '; '.join(total_message_parts),
                'data': all_results
            }

        logger.info(f"✅ 向量数据同步完成: {result['message']}")

        return VectorSyncResponse(
            success=result['success'],
            message=result['message'],
            data=result.get('data', {})
        )

    except Exception as e:
        logger.error(f"❌ 向量数据同步失败: {e}")
        return VectorSyncResponse(
            success=False,
            message=f"向量数据同步失败: {str(e)}",
            data={}
        )


@app.get("/vector/health")
async def vector_health_check():
    """向量存储健康检查"""
    try:
        # 初始化工厂（如果未初始化）
        if not brand_vector_factory.is_initialized:
            brand_vector_factory.initialize()

        health = brand_vector_factory.vector_store.health_check()

        return {
            "success": health['status'] == 'healthy',
            "status": health['status'],
            "message": health['message'],
            "collections": health['collections'],
            "timestamp": "2024-01-01T00:00:00Z"
        }

    except Exception as e:
        logger.error(f"❌ 向量存储健康检查失败: {e}")
        return {
            "success": False,
            "status": "error",
            "message": f"健康检查失败: {str(e)}",
            "collections": {},
            "timestamp": "2024-01-01T00:00:00Z"
        }


class SyncRequest(BaseModel):
    """同步请求模型"""
    shop_id: str
    incremental: bool = True
    sync_products: bool = True
    sync_qa: bool = True


@app.post("/sync")
async def sync_data(request: SyncRequest):
    """同步数据接口 - 从API增量同步产品和问答知识库数据"""
    try:
        from ..services.vector_store_service import VectorStoreService

        logger.info(f"🚀 开始同步数据 (店铺: {request.shop_id})")
        logger.info(f"📋 同步配置: 产品={request.sync_products}, 问答={request.sync_qa}, 增量={request.incremental}")

        # 创建向量存储服务
        vector_service = VectorStoreService()
        vector_service.initialize()

        results = {}

        # 同步产品数据
        if request.sync_products:
            try:
                product_count = vector_service.sync_products(request.shop_id, request.incremental)
                results['products'] = {
                    'success': True,
                    'count': product_count,
                    'message': f"成功同步 {product_count} 个产品"
                }
                logger.info(f"✅ 产品同步完成: {product_count} 个")
            except Exception as e:
                results['products'] = {
                    'success': False,
                    'count': 0,
                    'message': f"产品同步失败: {str(e)}"
                }
                logger.error(f"❌ 产品同步失败: {e}")

        # 同步问答知识库数据
        if request.sync_qa:
            try:
                qa_count = vector_service.sync_qa_from_api(request.shop_id, request.incremental)
                results['qa_knowledge'] = {
                    'success': True,
                    'count': qa_count,
                    'message': f"成功同步 {qa_count} 个问答"
                }
                logger.info(f"✅ 问答同步完成: {qa_count} 个")
            except Exception as e:
                results['qa_knowledge'] = {
                    'success': False,
                    'count': 0,
                    'message': f"问答同步失败: {str(e)}"
                }
                logger.error(f"❌ 问答同步失败: {e}")

        # 计算总体结果
        total_success = all(r.get('success', False) for r in results.values())
        total_count = sum(r.get('count', 0) for r in results.values())

        return {
            "success": total_success,
            "shop_id": request.shop_id,
            "incremental": request.incremental,
            "total_count": total_count,
            "results": results,
            "message": f"同步完成，共处理 {total_count} 条数据"
        }

    except Exception as e:
        logger.error(f"❌ 同步数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步数据失败: {str(e)}")


@app.post("/sync/products")
async def sync_products_only(shop_id: str, incremental: bool = True):
    """只同步产品数据"""
    try:
        from ..services.vector_store_service import VectorStoreService

        logger.info(f"🚀 开始同步产品数据 (店铺: {shop_id})")

        vector_service = VectorStoreService()
        vector_service.initialize()

        product_count = vector_service.sync_products(shop_id, incremental)

        return {
            "success": True,
            "shop_id": shop_id,
            "incremental": incremental,
            "type": "products",
            "count": product_count,
            "message": f"成功同步 {product_count} 个产品"
        }

    except Exception as e:
        logger.error(f"❌ 同步产品数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步产品数据失败: {str(e)}")


@app.post("/sync/qa")
async def sync_qa_only(shop_id: str, incremental: bool = True):
    """只同步问答知识库数据"""
    try:
        from ..services.vector_store_service import VectorStoreService

        logger.info(f"🚀 开始同步问答知识库数据 (店铺: {shop_id})")

        vector_service = VectorStoreService()
        vector_service.initialize()

        qa_count = vector_service.sync_qa_from_api(shop_id, incremental)

        return {
            "success": True,
            "shop_id": shop_id,
            "incremental": incremental,
            "type": "qa_knowledge",
            "count": qa_count,
            "message": f"成功同步 {qa_count} 个问答"
        }

    except Exception as e:
        logger.error(f"❌ 同步问答知识库数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步问答知识库数据失败: {str(e)}")


class SyncRequest(BaseModel):
    """同步请求模型"""
    shop_id: str
    incremental: bool = True
    sync_products: bool = True
    sync_qa: bool = True


@app.post("/sync")
async def sync_data(request: SyncRequest):
    """同步数据接口 - 从API增量同步产品和问答知识库数据"""
    try:
        from ..services.vector_store_service import VectorStoreService

        logger.info(f"🚀 开始同步数据 (店铺: {request.shop_id})")
        logger.info(f"📋 同步配置: 产品={request.sync_products}, 问答={request.sync_qa}, 增量={request.incremental}")

        # 创建向量存储服务
        vector_service = VectorStoreService()
        vector_service.initialize()

        results = {}

        # 同步产品数据
        if request.sync_products:
            try:
                product_count = vector_service.sync_products(request.shop_id, request.incremental)
                results['products'] = {
                    'success': True,
                    'count': product_count,
                    'message': f"成功同步 {product_count} 个产品"
                }
                logger.info(f"✅ 产品同步完成: {product_count} 个")
            except Exception as e:
                results['products'] = {
                    'success': False,
                    'count': 0,
                    'message': f"产品同步失败: {str(e)}"
                }
                logger.error(f"❌ 产品同步失败: {e}")

        # 同步问答知识库数据
        if request.sync_qa:
            try:
                qa_count = vector_service.sync_qa_from_api(request.shop_id, request.incremental)
                results['qa_knowledge'] = {
                    'success': True,
                    'count': qa_count,
                    'message': f"成功同步 {qa_count} 个问答"
                }
                logger.info(f"✅ 问答同步完成: {qa_count} 个")
            except Exception as e:
                results['qa_knowledge'] = {
                    'success': False,
                    'count': 0,
                    'message': f"问答同步失败: {str(e)}"
                }
                logger.error(f"❌ 问答同步失败: {e}")

        # 计算总体结果
        total_success = all(r.get('success', False) for r in results.values())
        total_count = sum(r.get('count', 0) for r in results.values())

        return {
            "success": total_success,
            "shop_id": request.shop_id,
            "incremental": request.incremental,
            "total_count": total_count,
            "results": results,
            "message": f"同步完成，共处理 {total_count} 条数据"
        }

    except Exception as e:
        logger.error(f"❌ 同步数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步数据失败: {str(e)}")


class ResetRequest(BaseModel):
    """重置请求模型"""
    conversation_id: Optional[str] = None


@app.post("/reset")
async def reset_conversation(request: ResetRequest = None):
    """重置对话历史"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        conversation_id = request.conversation_id if request else None
        brand_id = "ykwy"  # 默认使用易客无忧品牌

        success = brand_manager.reset_conversation(brand_id, conversation_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

        if conversation_id:
            logger.info(f"🔄 对话历史已重置: {conversation_id}")
            return {"message": f"对话已重置: {conversation_id}"}
        else:
            logger.info("🔄 所有对话历史已重置")
            return {"message": "所有对话已重置"}

    except Exception as e:
        logger.error(f"重置对话错误: {e}")
        raise HTTPException(status_code=500, detail=f"重置对话时出错: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.sales_agent.api.app:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
