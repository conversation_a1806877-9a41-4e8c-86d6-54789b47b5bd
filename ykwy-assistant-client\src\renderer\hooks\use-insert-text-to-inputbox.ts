import { useMutation } from '@tanstack/react-query'
import { apiClient } from '@/lib/api-client'

interface InsertTextParams {
  connectionId: string
  nickname: string
  text: string
}

interface InsertTextResponse {
  success: boolean
  message: string
  data?: unknown
  timestamp: string
}

/**
 * 发送文本到千牛输入框的Hook
 */
export function useInsertTextToInputbox() {
  return useMutation({
    mutationFn: async (params: InsertTextParams): Promise<InsertTextResponse> => {
      console.log('🚀 [发送到输入框] 开始发送:', params)

      if (!params.connectionId) {
        throw new Error('缺少connectionId参数')
      }

      if (!params.nickname) {
        throw new Error('缺少nickname参数')
      }

      if (!params.text) {
        throw new Error('缺少text参数')
      }

      const response = await apiClient.qianniu.insertTextToInputbox(params)

      console.log('✅ [发送到输入框] 发送成功:', response)
      return response
    },
    onSuccess: (data, variables) => {
      console.log('✅ [发送到输入框] 操作成功完成:', {
        nickname: variables.nickname,
        textLength: variables.text.length,
        response: data
      })
    },
    onError: (error, variables) => {
      console.error('❌ [发送到输入框] 操作失败:', {
        nickname: variables.nickname,
        textLength: variables.text.length,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  })
}
