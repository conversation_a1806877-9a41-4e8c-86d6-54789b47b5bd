import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve, join } from 'path'

export default defineConfig({
  plugins: [react()],
  root: resolve(__dirname, '../src/renderer'),
  base: './',
  publicDir: resolve(__dirname, '../public'),
  envDir: resolve(__dirname, '../'), // 指向项目根目录读取 .env 文件
  envPrefix: 'VITE_',
  define: {
    // 确保环境变量在构建时被正确替换
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development')
  },
  build: {
    outDir: resolve(__dirname, '../dist/renderer'),
    emptyOutDir: true,
    sourcemap: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../src/renderer'),
      '@shared': resolve(__dirname, '../src/shared'),
      '@main': resolve(__dirname, '../src/main')
    }
  },
  server: {
    port: parseInt(process.env.VITE_DEV_SERVER_PORT || '5174'),
    strictPort: true
  },
  optimizeDeps: {
    exclude: ['electron']
  }
})
