import { defineConfig, loadEnv } from 'vite'
import { resolve } from 'path'

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, resolve(__dirname, '../'), '')
  
  // 提取VITE_前缀的环境变量用于注入
  const viteEnvVars = Object.keys(env)
    .filter(key => key.startsWith('VITE_'))
    .reduce((acc, key) => {
      acc[`process.env.${key}`] = JSON.stringify(env[key])
      return acc
    }, {} as Record<string, string>)

  return {
    mode: process.env.NODE_ENV,
    root: resolve(__dirname, '../src/main'),
    envDir: resolve(__dirname, '../'),
    envPrefix: 'VITE_',
    
    // 将环境变量注入到主进程构建中
    define: {
      ...viteEnvVars,
      // 添加常用的环境变量
      'process.env.NODE_ENV': JSON.stringify(mode),
      'process.env.ELECTRON_DISABLE_SECURITY_WARNINGS': JSON.stringify(env.ELECTRON_DISABLE_SECURITY_WARNINGS || 'true')
    },
    
    build: {
      outDir: resolve(__dirname, '../dist/main'),
      emptyOutDir: true,
      minify: process.env.NODE_ENV === 'production',
      sourcemap: true,
      // 指定为 Node.js 环境构建
      target: 'node18',
      lib: {
        entry: resolve(__dirname, '../src/main/index.ts'),
        formats: ['cjs'],
        fileName: () => 'index.js'
      },
      rollupOptions: {
        external: [
          'electron',
          // Node.js 内置模块
          'path',
          'fs',
          'os',
          'url',
          'crypto',
          'events',
          'stream',
          'util',
          'buffer',
          // 所有 node: 前缀的模块
          /^node:/,
          // 项目依赖
          ...Object.keys(require(resolve(__dirname, '../package.json')).dependencies || {})
        ]
      }
    },
    resolve: {
      alias: {
        '@shared': resolve(__dirname, '../src/shared'),
        '@main': resolve(__dirname, '../src/main'),
      }
    }
  }
}) 